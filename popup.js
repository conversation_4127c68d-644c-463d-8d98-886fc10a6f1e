document.addEventListener('DOMContentLoaded', function() {
  // تحميل الإجراءات المحفوظة
  chrome.storage.local.get('actions', function(data) {
    const actions = data.actions || [];
    renderActionsList(actions);
  });
  
  // إضافة إجراء جديد
  document.getElementById('addAction').addEventListener('click', function() {
    const actionType = document.getElementById('actionType').value;
    const waitTime = document.getElementById('waitTime').value;
    
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        command: "getSelectedElement"
      }, function(response) {
        if (response && response.selector) {
          addAction({
            type: actionType,
            selector: response.selector,
            waitTime: waitTime || 0
          });
        } else {
          alert("الرجاء تحديد عنصر في الصفحة أولاً");
        }
      });
    });
  });
  
  // تنفيذ الإجراءات
  document.getElementById('executeAction').addEventListener('click', function() {
    chrome.storage.local.get('actions', function(data) {
      const actions = data.actions || [];
      if (actions.length > 0) {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
          chrome.tabs.sendMessage(tabs[0].id, {
            command: "executeActions",
            actions: actions
          });
        });
      }
    });
  });
});

// إضافة إجراء جديد وحفظه
function addAction(action) {
  chrome.storage.local.get('actions', function(data) {
    const actions = data.actions || [];
    actions.push(action);
    chrome.storage.local.set({actions: actions}, function() {
      renderActionsList(actions);
    });
  });
}

// عرض قائمة الإجراءات
function renderActionsList(actions) {
  const list = document.getElementById('actionsList');
  list.innerHTML = '';
  
  actions.forEach((action, index) => {
    const item = document.createElement('div');
    item.className = 'action-item';
    item.innerHTML = `
      <span>${action.type}</span>
      <span>${action.selector}</span>
      <span>${action.waitTime}ms</span>
      <button class="delete-btn" data-index="${index}">حذف</button>
    `;
    list.appendChild(item);
  });
  
  // إضافة أحداث أزرار الحذف
  document.querySelectorAll('.delete-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const index = parseInt(this.getAttribute('data-index'));
      removeAction(index);
    });
  });
}

// حذف إجراء
function removeAction(index) {
  chrome.storage.local.get('actions', function(data) {
    const actions = data.actions || [];
    actions.splice(index, 1);
    chrome.storage.local.set({actions: actions}, function() {
      renderActionsList(actions);
    });
  });
}
