/**
 * أداة الأتمتة الذكية - منطق الواجهة الرئيسي
 * إدارة التفاعل مع المستخدم والإجراءات
 */

// متغيرات عامة
let actions = [];
let isRecording = false;
let recordingStartTime = null;

// تهيئة الإضافة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function () {
  initializeExtension();
  bindEvents();
  loadSavedActions();
  updateUI();
});

/**
 * تهيئة الإضافة
 */
function initializeExtension() {
  console.log('تم تحميل أداة الأتمتة الذكية');
  updateConnectionStatus();
}

/**
 * ربط الأحداث بالعناصر
 */
function bindEvents() {
  // أزرار التسجيل
  document.getElementById('startRecording').addEventListener('click', startRecording);
  document.getElementById('stopRecording').addEventListener('click', stopRecording);

  // نموذج الإجراء اليدوي
  document.getElementById('actionType').addEventListener('change', updateActionForm);
  document.getElementById('selectElement').addEventListener('click', selectElement);
  document.getElementById('addAction').addEventListener('click', addManualAction);
  document.getElementById('testAction').addEventListener('click', testAction);

  // إدارة الإجراءات
  document.getElementById('executeActions').addEventListener('click', executeActions);
  document.getElementById('clearActions').addEventListener('click', clearActions);
}

/**
 * بدء تسجيل الإجراءات
 */
async function startRecording() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'startRecording' });

    if (response && response.success) {
      isRecording = true;
      recordingStartTime = Date.now();
      updateRecordingUI();
      showNotification('تم بدء التسجيل بنجاح', 'success');
    } else {
      showNotification('فشل في بدء التسجيل', 'error');
    }
  } catch (error) {
    console.error('خطأ في بدء التسجيل:', error);
    showNotification('خطأ في الاتصال بالصفحة', 'error');
  }
}

/**
 * إيقاف تسجيل الإجراءات
 */
async function stopRecording() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'stopRecording' });

    if (response && response.success) {
      isRecording = false;

      if (response.actions && response.actions.length > 0) {
        actions = [...actions, ...response.actions];
        saveActions();
        renderActionsList();
        showNotification(`تم تسجيل ${response.actions.length} إجراء`, 'success');
      }

      updateRecordingUI();
    } else {
      showNotification('فشل في إيقاف التسجيل', 'error');
    }
  } catch (error) {
    console.error('خطأ في إيقاف التسجيل:', error);
    showNotification('خطأ في الاتصال بالصفحة', 'error');
  }
}

/**
 * تحديث نموذج الإجراء حسب النوع
 */
function updateActionForm() {
  const actionType = document.getElementById('actionType').value;
  const valueGroup = document.getElementById('actionValueGroup');
  const valueInput = document.getElementById('actionValue');

  switch (actionType) {
    case 'click':
      valueGroup.style.display = 'none';
      break;
    case 'type':
      valueGroup.style.display = 'block';
      valueInput.placeholder = 'النص المراد كتابته';
      break;
    case 'wait':
      valueGroup.style.display = 'block';
      valueInput.placeholder = 'مدة الانتظار بالمللي ثانية';
      break;
    case 'scroll':
      valueGroup.style.display = 'block';
      valueInput.placeholder = 'الاتجاه: up, down, left, right';
      break;
    default:
      valueGroup.style.display = 'block';
      valueInput.placeholder = 'القيمة';
  }
}

/**
 * تحديد عنصر من الصفحة
 */
async function selectElement() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    // إغلاق النافذة للسماح بتحديد العنصر
    window.close();

    const response = await chrome.tabs.sendMessage(tab.id, { action: 'selectElement' });

    if (response && response.success) {
      document.getElementById('elementSelector').value = response.selector;
    }
  } catch (error) {
    console.error('خطأ في تحديد العنصر:', error);
    showNotification('فشل في تحديد العنصر', 'error');
  }
}

/**
 * إضافة إجراء يدوي
 */
function addManualAction() {
  const actionType = document.getElementById('actionType').value;
  const selector = document.getElementById('elementSelector').value;
  const value = document.getElementById('actionValue').value;
  const waitTime = parseInt(document.getElementById('waitTime').value) || 1000;

  if (!actionType) {
    showNotification('يرجى اختيار نوع الإجراء', 'warning');
    return;
  }

  if (['click', 'type'].includes(actionType) && !selector) {
    showNotification('يرجى تحديد العنصر', 'warning');
    return;
  }

  const action = {
    id: generateId(),
    type: actionType,
    selector: selector,
    value: value,
    waitTime: waitTime,
    timestamp: Date.now(),
    manual: true
  };

  actions.push(action);
  saveActions();
  renderActionsList();
  clearActionForm();
  showNotification('تم إضافة الإجراء بنجاح', 'success');
}

/**
 * اختبار إجراء واحد
 */
async function testAction() {
  const actionType = document.getElementById('actionType').value;
  const selector = document.getElementById('elementSelector').value;
  const value = document.getElementById('actionValue').value;

  if (!actionType) {
    showNotification('يرجى اختيار نوع الإجراء', 'warning');
    return;
  }

  const testAction = {
    type: actionType,
    selector: selector,
    value: value,
    waitTime: 0
  };

  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const response = await chrome.tabs.sendMessage(tab.id, {
      action: 'executeActions',
      actions: [testAction]
    });

    if (response && response.success) {
      showNotification('تم اختبار الإجراء بنجاح', 'success');
    } else {
      showNotification('فشل في اختبار الإجراء', 'error');
    }
  } catch (error) {
    console.error('خطأ في اختبار الإجراء:', error);
    showNotification('خطأ في الاتصال بالصفحة', 'error');
  }
}

/**
 * تنفيذ جميع الإجراءات
 */
async function executeActions() {
  if (actions.length === 0) {
    showNotification('لا توجد إجراءات للتنفيذ', 'warning');
    return;
  }

  const speed = document.getElementById('executionSpeed').value;

  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    showNotification('بدء تنفيذ الإجراءات...', 'info');

    const response = await chrome.tabs.sendMessage(tab.id, {
      action: 'executeActions',
      actions: actions,
      speed: speed
    });

    if (response && response.success) {
      showNotification(`تم تنفيذ ${actions.length} إجراء بنجاح`, 'success');
    } else {
      showNotification('فشل في تنفيذ الإجراءات', 'error');
    }
  } catch (error) {
    console.error('خطأ في تنفيذ الإجراءات:', error);
    showNotification('خطأ في الاتصال بالصفحة', 'error');
  }
}

/**
 * مسح جميع الإجراءات
 */
function clearActions() {
  if (actions.length === 0) return;

  if (confirm('هل أنت متأكد من حذف جميع الإجراءات؟')) {
    actions = [];
    saveActions();
    renderActionsList();
    updateUI();
    showNotification('تم حذف جميع الإجراءات', 'info');
  }
}

/**
 * عرض قائمة الإجراءات
 */
function renderActionsList() {
  const listContainer = document.getElementById('actionsList');

  if (actions.length === 0) {
    listContainer.innerHTML = `
      <div class="empty-state">
        <div class="empty-icon">📝</div>
        <h4>لا توجد إجراءات</h4>
        <p>ابدأ بتسجيل الإجراءات أو إضافة إجراء يدوي</p>
      </div>
    `;
    return;
  }

  const actionsHTML = actions.map((action, index) => `
    <div class="action-item" data-index="${index}">
      <div class="action-icon">
        ${getActionIcon(action.type)}
      </div>
      <div class="action-content">
        <div class="action-title">${getActionTitle(action.type)}</div>
        <div class="action-details">${getActionDetails(action)}</div>
      </div>
      <div class="action-controls">
        <button class="btn btn-outline btn-small" onclick="deleteAction(${index})" title="حذف">
          🗑️
        </button>
      </div>
    </div>
  `).join('');

  listContainer.innerHTML = actionsHTML;
}

/**
 * الحصول على أيقونة الإجراء
 */
function getActionIcon(type) {
  const icons = {
    click: '👆',
    type: '⌨️',
    wait: '⏰',
    scroll: '📜'
  };
  return icons[type] || '⚙️';
}

/**
 * الحصول على عنوان الإجراء
 */
function getActionTitle(type) {
  const titles = {
    click: 'نقر',
    type: 'كتابة نص',
    wait: 'انتظار',
    scroll: 'تمرير'
  };
  return titles[type] || type;
}

/**
 * الحصول على تفاصيل الإجراء
 */
function getActionDetails(action) {
  if (action.selector) {
    const shortSelector = action.selector.length > 30 ?
      action.selector.substring(0, 30) + '...' : action.selector;
    return action.value ?
      `${shortSelector} - "${action.value}"` : shortSelector;
  }
  return action.value || '';
}

/**
 * حذف إجراء محدد
 */
function deleteAction(index) {
  if (confirm('هل أنت متأكد من حذف هذا الإجراء؟')) {
    actions.splice(index, 1);
    saveActions();
    renderActionsList();
    updateUI();
    showNotification('تم حذف الإجراء', 'info');
  }
}

/**
 * مسح نموذج الإجراء
 */
function clearActionForm() {
  document.getElementById('elementSelector').value = '';
  document.getElementById('actionValue').value = '';
  document.getElementById('waitTime').value = '1000';
}

/**
 * تحديث واجهة التسجيل
 */
function updateRecordingUI() {
  const statusElement = document.getElementById('recordingStatus');
  const startBtn = document.getElementById('startRecording');
  const stopBtn = document.getElementById('stopRecording');
  const countElement = document.getElementById('recordedCount');

  if (isRecording) {
    statusElement.classList.add('active');
    statusElement.querySelector('.status-text').textContent = 'جاري التسجيل';

    startBtn.disabled = true;
    stopBtn.disabled = false;
  } else {
    statusElement.classList.remove('active');
    statusElement.querySelector('.status-text').textContent = 'غير نشط';

    startBtn.disabled = false;
    stopBtn.disabled = true;
  }

  if (countElement) {
    countElement.textContent = actions.length.toString();
  }
}

/**
 * تحديث الواجهة العامة
 */
function updateUI() {
  updateRecordingUI();
  renderActionsList();
  updateConnectionStatus();
}

/**
 * تحديث حالة الاتصال
 */
async function updateConnectionStatus() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'ping' });

    const statusDot = document.getElementById('connectionStatus');
    const statusText = document.getElementById('statusText');

    if (response && response.status === 'ready') {
      statusDot.classList.remove('error', 'warning');
      statusText.textContent = 'متصل';
    } else {
      statusDot.classList.add('warning');
      statusText.textContent = 'غير متصل';
    }
  } catch (error) {
    const statusDot = document.getElementById('connectionStatus');
    const statusText = document.getElementById('statusText');

    statusDot.classList.add('error');
    statusText.textContent = 'خطأ في الاتصال';
  }
}

/**
 * حفظ الإجراءات في التخزين المحلي
 */
function saveActions() {
  chrome.storage.local.set({ actions: actions }, function () {
    if (chrome.runtime.lastError) {
      console.error('خطأ في حفظ الإجراءات:', chrome.runtime.lastError);
    }
  });
}

/**
 * تحميل الإجراءات المحفوظة
 */
function loadSavedActions() {
  chrome.storage.local.get(['actions'], function (result) {
    if (result.actions && Array.isArray(result.actions)) {
      actions = result.actions;
      renderActionsList();
      updateUI();
    }
  });
}

/**
 * عرض إشعار للمستخدم
 */
function showNotification(message, type = 'info') {
  // إنشاء عنصر الإشعار
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${getNotificationColor(type)};
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    font-size: 13px;
    max-width: 300px;
    animation: slideIn 0.3s ease;
  `;

  notification.textContent = message;

  // إضافة الأنيميشن
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
  `;
  document.head.appendChild(style);

  // إضافة إلى الصفحة
  document.body.appendChild(notification);

  // إزالة تلقائية بعد 3 ثواني
  setTimeout(() => {
    if (notification.parentNode) {
      notification.style.animation = 'slideIn 0.3s ease reverse';
      setTimeout(() => notification.remove(), 300);
    }
  }, 3000);
}

/**
 * الحصول على لون الإشعار
 */
function getNotificationColor(type) {
  const colors = {
    success: '#28a745',
    error: '#dc3545',
    warning: '#ffc107',
    info: '#17a2b8'
  };
  return colors[type] || colors.info;
}

/**
 * توليد معرف فريد
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}
