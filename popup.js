/**
 * WebFlow Automator Pro - Main Popup Script
 * Entry point for the popup interface
 */

// Global instances
let uiManager = null;
let projectManager = null;
let templateManager = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', async function() {
  try {
    // Initialize managers
    uiManager = new UIManager();

    // Initialize project manager if available
    if (typeof ProjectManager !== 'undefined') {
      projectManager = new ProjectManager();
    }

    // Initialize template manager if available
    if (typeof TemplateManager !== 'undefined') {
      templateManager = new TemplateManager();
    }

    // Load saved data
    await loadSavedData();

    // Update UI
    uiManager.updateUI();

    console.log('WebFlow Automator Pro popup initialized successfully');

  } catch (error) {
    console.error('Failed to initialize popup:', error);
    showErrorMessage('فشل في تحميل الإضافة. يرجى إعادة تحميل الصفحة.');
  }
});

/**
 * Load saved data from storage
 */
async function loadSavedData() {
  try {
    const data = await StorageUtils.get(['actions', 'projects', 'settings', 'templates']);

    // Load actions (for backward compatibility)
    if (data.actions && Array.isArray(data.actions)) {
      uiManager.actions = data.actions;
    }

    // Load projects
    if (data.projects && Array.isArray(data.projects) && projectManager) {
      projectManager.projects = data.projects;
    }

    // Load settings
    if (data.settings && uiManager) {
      uiManager.settings = data.settings;
    }

    // Load templates
    if (data.templates && Array.isArray(data.templates) && templateManager) {
      templateManager.templates = data.templates;
    }

  } catch (error) {
    console.error('Failed to load saved data:', error);
  }
}

/**
 * Save current state to storage
 */
async function saveCurrentState() {
  try {
    const dataToSave = {};

    // Save actions
    if (uiManager && uiManager.actions) {
      dataToSave.actions = uiManager.actions;
    }

    // Save projects
    if (projectManager && projectManager.projects) {
      dataToSave.projects = projectManager.projects;
    }

    // Save settings
    if (uiManager && uiManager.settings) {
      dataToSave.settings = uiManager.settings;
    }

    await StorageUtils.set(dataToSave);

  } catch (error) {
    console.error('Failed to save current state:', error);
  }
}

/**
 * Handle extension errors
 */
function handleExtensionError(error, context = '') {
  console.error(`[WebFlow Automator] ${context}:`, error);

  let userMessage = 'حدث خطأ غير متوقع';

  if (error.message) {
    if (error.message.includes('Extension context invalidated')) {
      userMessage = 'تم تحديث الإضافة. يرجى إعادة تحميل الصفحة.';
    } else if (error.message.includes('Could not establish connection')) {
      userMessage = 'لا يمكن الاتصال بالصفحة. تأكد من أن الصفحة محملة بالكامل.';
    } else if (error.message.includes('Permission denied')) {
      userMessage = 'لا توجد صلاحية للوصول إلى هذه الصفحة.';
    }
  }

  showErrorMessage(userMessage);
}

/**
 * Show error message to user
 */
function showErrorMessage(message) {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.innerHTML = `
    <div class="error-content">
      <i class="icon-error"></i>
      <span>${message}</span>
      <button class="btn-close" onclick="this.parentElement.parentElement.remove()">
        <i class="icon-close"></i>
      </button>
    </div>
  `;

  // Add error styles if not already present
  if (!document.querySelector('.error-message-styles')) {
    const styles = document.createElement('style');
    styles.className = 'error-message-styles';
    styles.textContent = `
      .error-message {
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        background: #ff4757;
        color: white;
        padding: 12px;
        border-radius: 6px;
        z-index: 10000;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      }
      .error-content {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .error-content .btn-close {
        margin-right: auto;
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 2px;
      }
    `;
    document.head.appendChild(styles);
  }

  document.body.appendChild(errorDiv);

  // Auto remove after 5 seconds
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.remove();
    }
  }, 5000);
}

/**
 * Handle page visibility changes
 */
document.addEventListener('visibilitychange', function() {
  if (document.visibilityState === 'visible') {
    // Refresh UI when popup becomes visible
    if (uiManager) {
      uiManager.updateConnectionStatus();
    }
  } else if (document.visibilityState === 'hidden') {
    // Save state when popup is hidden
    saveCurrentState();
  }
});

/**
 * Handle popup close
 */
window.addEventListener('beforeunload', function() {
  // Save current state before closing
  saveCurrentState();

  // Stop any ongoing recordings
  if (uiManager && uiManager.isRecording) {
    uiManager.stopRecording();
  }
});

/**
 * Global error handler
 */
window.addEventListener('error', function(event) {
  handleExtensionError(event.error, 'Global Error');
});

/**
 * Unhandled promise rejection handler
 */
window.addEventListener('unhandledrejection', function(event) {
  handleExtensionError(event.reason, 'Unhandled Promise Rejection');
});

/**
 * Chrome extension error handler
 */
if (chrome.runtime) {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'error') {
      handleExtensionError(new Error(message.error), message.context);
    }
  });
}

// Export global functions for HTML event handlers
window.uiManager = null; // Will be set after initialization
window.projectManager = null; // Will be set after initialization
window.templateManager = null; // Will be set after initialization

// Set global references after initialization
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    window.uiManager = uiManager;
    window.projectManager = projectManager;
    window.templateManager = templateManager;
  }, 100);
});
