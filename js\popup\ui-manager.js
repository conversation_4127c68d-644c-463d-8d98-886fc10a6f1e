/**
 * WebFlow Automator Pro - UI Manager
 * Handles all UI interactions and state management for the popup
 */

class UIManager {
  constructor() {
    this.currentTab = 'recorder';
    this.isRecording = false;
    this.recordingStartTime = null;
    this.recordingTimer = null;
    this.actions = [];
    this.settings = {};
    
    this.init();
  }

  init() {
    this.bindEvents();
    this.loadSettings();
    this.updateUI();
  }

  bindEvents() {
    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // Recording controls
    document.getElementById('startRecording')?.addEventListener('click', () => {
      this.startRecording();
    });

    document.getElementById('stopRecording')?.addEventListener('click', () => {
      this.stopRecording();
    });

    document.getElementById('pauseRecording')?.addEventListener('click', () => {
      this.pauseRecording();
    });

    // Action form
    document.getElementById('actionType')?.addEventListener('change', (e) => {
      this.updateActionForm(e.target.value);
    });

    document.getElementById('selectElement')?.addEventListener('click', () => {
      this.startElementSelection();
    });

    document.getElementById('addAction')?.addEventListener('click', () => {
      this.addManualAction();
    });

    document.getElementById('testAction')?.addEventListener('click', () => {
      this.testAction();
    });

    // Actions list controls
    document.getElementById('executeActions')?.addEventListener('click', () => {
      this.executeActions();
    });

    document.getElementById('clearActions')?.addEventListener('click', () => {
      this.clearActions();
    });

    // Settings and help
    document.getElementById('settingsBtn')?.addEventListener('click', () => {
      this.openSettings();
    });

    document.getElementById('helpBtn')?.addEventListener('click', () => {
      this.openHelp();
    });

    // Project management
    document.getElementById('newProject')?.addEventListener('click', () => {
      this.createNewProject();
    });

    document.getElementById('importProject')?.addEventListener('click', () => {
      this.importProject();
    });
  }

  async loadSettings() {
    try {
      const result = await chrome.runtime.sendMessage({ action: 'getSettings' });
      if (result.success) {
        this.settings = result.settings || {};
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`)?.classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`)?.classList.add('active');

    this.currentTab = tabName;
    this.updateUI();
  }

  async startRecording() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const response = await chrome.tabs.sendMessage(tab.id, { action: 'startRecording' });
      
      if (response.success) {
        this.isRecording = true;
        this.recordingStartTime = Date.now();
        this.startRecordingTimer();
        this.updateRecordingUI();
        this.showNotification('تم بدء التسجيل', 'success');
      } else {
        this.showNotification('فشل في بدء التسجيل: ' + response.error, 'error');
      }
    } catch (error) {
      this.showNotification('خطأ في الاتصال بالصفحة', 'error');
    }
  }

  async stopRecording() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const response = await chrome.tabs.sendMessage(tab.id, { action: 'stopRecording' });
      
      if (response.success) {
        this.isRecording = false;
        this.stopRecordingTimer();
        
        if (response.actions && response.actions.length > 0) {
          this.actions = [...this.actions, ...response.actions];
          this.renderActionsList();
          this.showNotification(`تم تسجيل ${response.actions.length} إجراء`, 'success');
        }
        
        this.updateRecordingUI();
      } else {
        this.showNotification('فشل في إيقاف التسجيل: ' + response.error, 'error');
      }
    } catch (error) {
      this.showNotification('خطأ في الاتصال بالصفحة', 'error');
    }
  }

  pauseRecording() {
    // Implementation for pause functionality
    this.showNotification('ميزة الإيقاف المؤقت قيد التطوير', 'info');
  }

  startRecordingTimer() {
    this.recordingTimer = setInterval(() => {
      const elapsed = Date.now() - this.recordingStartTime;
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);
      const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      
      const durationElement = document.getElementById('recordingDuration');
      if (durationElement) {
        durationElement.textContent = timeString;
      }
    }, 1000);
  }

  stopRecordingTimer() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }

  updateRecordingUI() {
    const statusElement = document.getElementById('recordingStatus');
    const startBtn = document.getElementById('startRecording');
    const stopBtn = document.getElementById('stopRecording');
    const pauseBtn = document.getElementById('pauseRecording');
    const countElement = document.getElementById('recordedCount');

    if (this.isRecording) {
      statusElement?.classList.add('active');
      statusElement.querySelector('.status-text').textContent = 'جاري التسجيل';
      
      startBtn.disabled = true;
      stopBtn.disabled = false;
      pauseBtn.disabled = false;
    } else {
      statusElement?.classList.remove('active');
      statusElement.querySelector('.status-text').textContent = 'غير نشط';
      
      startBtn.disabled = false;
      stopBtn.disabled = true;
      pauseBtn.disabled = true;
      
      const durationElement = document.getElementById('recordingDuration');
      if (durationElement) {
        durationElement.textContent = '00:00';
      }
    }

    if (countElement) {
      countElement.textContent = this.actions.length.toString();
    }
  }

  updateActionForm(actionType) {
    const valueGroup = document.getElementById('actionValueGroup');
    const valueInput = document.getElementById('actionValue');
    const selectorGroup = document.querySelector('[for="elementSelector"]').parentElement;

    if (!valueGroup || !valueInput) return;

    // Show/hide and update value field based on action type
    switch (actionType) {
      case 'click':
      case 'hover':
        valueGroup.style.display = 'none';
        selectorGroup.style.display = 'block';
        break;
      case 'type':
        valueGroup.style.display = 'block';
        valueInput.placeholder = 'النص المراد كتابته';
        selectorGroup.style.display = 'block';
        break;
      case 'select':
        valueGroup.style.display = 'block';
        valueInput.placeholder = 'القيمة أو النص المراد اختياره';
        selectorGroup.style.display = 'block';
        break;
      case 'wait':
        valueGroup.style.display = 'block';
        valueInput.placeholder = 'مدة الانتظار بالمللي ثانية';
        selectorGroup.style.display = 'none';
        break;
      case 'scroll':
        valueGroup.style.display = 'block';
        valueInput.placeholder = 'الاتجاه: up, down, left, right';
        selectorGroup.style.display = 'none';
        break;
      case 'extract':
        valueGroup.style.display = 'block';
        valueInput.placeholder = 'اسم المتغير لحفظ البيانات';
        selectorGroup.style.display = 'block';
        break;
      case 'variable':
        valueGroup.style.display = 'block';
        valueInput.placeholder = 'قيمة المتغير';
        selectorGroup.style.display = 'none';
        break;
      case 'javascript':
        valueGroup.style.display = 'block';
        valueInput.placeholder = 'كود JavaScript';
        selectorGroup.style.display = 'none';
        break;
      default:
        valueGroup.style.display = 'block';
        valueInput.placeholder = 'القيمة';
        selectorGroup.style.display = 'block';
    }
  }

  async startElementSelection() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // Close popup to allow element selection
      window.close();
      
      const response = await chrome.tabs.sendMessage(tab.id, { action: 'startElementSelection' });
      
      if (response.success) {
        const selectorInput = document.getElementById('elementSelector');
        if (selectorInput) {
          selectorInput.value = response.selector;
        }
      }
    } catch (error) {
      this.showNotification('فشل في تحديد العنصر', 'error');
    }
  }

  addManualAction() {
    const actionType = document.getElementById('actionType')?.value;
    const selector = document.getElementById('elementSelector')?.value;
    const value = document.getElementById('actionValue')?.value;
    const waitBefore = parseInt(document.getElementById('waitBefore')?.value) || 0;
    const waitAfter = parseInt(document.getElementById('waitAfter')?.value) || 0;

    if (!actionType) {
      this.showNotification('يرجى اختيار نوع الإجراء', 'warning');
      return;
    }

    if (['click', 'type', 'select', 'hover', 'extract'].includes(actionType) && !selector) {
      this.showNotification('يرجى تحديد العنصر', 'warning');
      return;
    }

    const action = {
      id: StringUtils.generateId(),
      type: actionType,
      selector: selector,
      value: value,
      waitBefore: waitBefore,
      waitAfter: waitAfter,
      timestamp: Date.now(),
      manual: true
    };

    this.actions.push(action);
    this.renderActionsList();
    this.clearActionForm();
    this.showNotification('تم إضافة الإجراء بنجاح', 'success');
  }

  async testAction() {
    const actionType = document.getElementById('actionType')?.value;
    const selector = document.getElementById('elementSelector')?.value;
    const value = document.getElementById('actionValue')?.value;

    if (!actionType) {
      this.showNotification('يرجى اختيار نوع الإجراء', 'warning');
      return;
    }

    const testAction = {
      type: actionType,
      selector: selector,
      value: value,
      waitBefore: 0,
      waitAfter: 0
    };

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'executeActions',
        actions: [testAction],
        options: { speed: 'instant', errorHandling: 'stop' }
      });

      if (response.success) {
        this.showNotification('تم اختبار الإجراء بنجاح', 'success');
      } else {
        this.showNotification('فشل في اختبار الإجراء: ' + response.error, 'error');
      }
    } catch (error) {
      this.showNotification('خطأ في الاتصال بالصفحة', 'error');
    }
  }

  clearActionForm() {
    document.getElementById('elementSelector').value = '';
    document.getElementById('actionValue').value = '';
    document.getElementById('waitBefore').value = '0';
    document.getElementById('waitAfter').value = '0';
  }

  async executeActions() {
    if (this.actions.length === 0) {
      this.showNotification('لا توجد إجراءات للتنفيذ', 'warning');
      return;
    }

    const speed = document.getElementById('executionSpeed')?.value || 'normal';
    const errorHandling = document.getElementById('errorHandling')?.value || 'continue';
    const highlightElements = document.getElementById('highlightElements')?.checked || false;

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      this.showNotification('بدء تنفيذ الإجراءات...', 'info');
      
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'executeActions',
        actions: this.actions,
        options: {
          speed: speed,
          errorHandling: errorHandling,
          highlightElements: highlightElements
        }
      });

      if (response.success) {
        const execution = response.execution;
        const successCount = execution.results.filter(r => r.success).length;
        const errorCount = execution.errors.length;
        
        if (errorCount === 0) {
          this.showNotification(`تم تنفيذ جميع الإجراءات بنجاح (${successCount})`, 'success');
        } else {
          this.showNotification(`تم تنفيذ ${successCount} إجراء، فشل ${errorCount}`, 'warning');
        }
      } else {
        this.showNotification('فشل في تنفيذ الإجراءات: ' + response.error, 'error');
      }
    } catch (error) {
      this.showNotification('خطأ في الاتصال بالصفحة', 'error');
    }
  }

  clearActions() {
    if (this.actions.length === 0) return;

    if (confirm('هل أنت متأكد من حذف جميع الإجراءات؟')) {
      this.actions = [];
      this.renderActionsList();
      this.updateRecordingUI();
      this.showNotification('تم حذف جميع الإجراءات', 'info');
    }
  }

  renderActionsList() {
    const listContainer = document.getElementById('actionsList');
    if (!listContainer) return;

    if (this.actions.length === 0) {
      listContainer.innerHTML = `
        <div class="empty-state">
          <i class="icon-list"></i>
          <h4>لا توجد إجراءات</h4>
          <p>ابدأ بتسجيل الإجراءات أو إضافة إجراء يدوي</p>
        </div>
      `;
      return;
    }

    const actionsHTML = this.actions.map((action, index) => `
      <div class="action-item" data-index="${index}">
        <div class="action-icon ${action.type}">
          <i class="icon-${action.type}"></i>
        </div>
        <div class="action-content">
          <div class="action-title">${this.getActionTitle(action)}</div>
          <div class="action-details">${this.getActionDetails(action)}</div>
        </div>
        <div class="action-controls">
          <button class="btn btn-outline btn-small" onclick="uiManager.editAction(${index})" title="تعديل">
            <i class="icon-edit"></i>
          </button>
          <button class="btn btn-outline btn-small" onclick="uiManager.deleteAction(${index})" title="حذف">
            <i class="icon-trash"></i>
          </button>
        </div>
      </div>
    `).join('');

    listContainer.innerHTML = actionsHTML;
  }

  getActionTitle(action) {
    const titles = {
      click: 'نقر',
      type: 'كتابة نص',
      select: 'اختيار من قائمة',
      hover: 'تمرير الماوس',
      scroll: 'تمرير الصفحة',
      wait: 'انتظار',
      extract: 'استخراج بيانات',
      condition: 'شرط',
      loop: 'حلقة تكرار',
      variable: 'متغير',
      javascript: 'كود JavaScript'
    };
    return titles[action.type] || action.type;
  }

  getActionDetails(action) {
    if (action.selector) {
      const shortSelector = StringUtils.truncate(action.selector, 30);
      return action.value ? `${shortSelector} - "${StringUtils.truncate(action.value, 20)}"` : shortSelector;
    }
    return action.value ? StringUtils.truncate(action.value, 40) : '';
  }

  editAction(index) {
    // Implementation for editing actions
    this.showNotification('ميزة التعديل قيد التطوير', 'info');
  }

  deleteAction(index) {
    if (confirm('هل أنت متأكد من حذف هذا الإجراء؟')) {
      this.actions.splice(index, 1);
      this.renderActionsList();
      this.updateRecordingUI();
      this.showNotification('تم حذف الإجراء', 'info');
    }
  }

  createNewProject() {
    this.showNotification('ميزة المشاريع قيد التطوير', 'info');
  }

  importProject() {
    this.showNotification('ميزة الاستيراد قيد التطوير', 'info');
  }

  openSettings() {
    this.showNotification('ميزة الإعدادات قيد التطوير', 'info');
  }

  openHelp() {
    chrome.tabs.create({ url: 'https://webflow-automator.com/help' });
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="icon-${type}"></i>
        <span>${message}</span>
      </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }

  updateUI() {
    this.updateRecordingUI();
    this.renderActionsList();
    
    // Update connection status
    this.updateConnectionStatus();
  }

  async updateConnectionStatus() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const response = await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
      
      const statusDot = document.getElementById('connectionStatus');
      const statusText = document.getElementById('statusText');
      
      if (response && response.status === 'ready') {
        statusDot?.classList.remove('error', 'warning');
        statusText.textContent = 'متصل';
      } else {
        statusDot?.classList.add('warning');
        statusText.textContent = 'غير متصل';
      }
    } catch (error) {
      const statusDot = document.getElementById('connectionStatus');
      const statusText = document.getElementById('statusText');
      
      statusDot?.classList.add('error');
      statusText.textContent = 'خطأ في الاتصال';
    }
  }
}

// Export for global use
if (typeof window !== 'undefined') {
  window.UIManager = UIManager;
}
