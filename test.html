<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة اختبار WebFlow Automator Pro</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .section h2 {
            color: #764ba2;
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            color: #155724;
            display: none;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .data-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .highlight {
            background: yellow !important;
            transition: background 0.3s ease;
        }
        
        .hidden {
            display: none;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
        }
        
        .close-modal {
            float: left;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .close-modal:hover {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 صفحة اختبار WebFlow Automator Pro</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            استخدم هذه الصفحة لاختبار جميع ميزات إضافة WebFlow Automator Pro
        </p>

        <!-- قسم تسجيل الدخول -->
        <div class="section">
            <h2>📝 نموذج تسجيل الدخول</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني:</label>
                    <input type="email" id="email" name="email" placeholder="أدخل بريدك الإلكتروني">
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" placeholder="أدخل كلمة المرور">
                </div>
                <div class="form-group">
                    <label for="remember">
                        <input type="checkbox" id="remember" name="remember" style="width: auto; margin-left: 8px;">
                        تذكرني
                    </label>
                </div>
                <button type="submit" class="btn-primary">تسجيل الدخول</button>
            </form>
        </div>

        <!-- قسم النماذج المتقدمة -->
        <div class="section">
            <h2>📋 نموذج متقدم</h2>
            <form id="advancedForm">
                <div class="form-group">
                    <label for="fullName">الاسم الكامل:</label>
                    <input type="text" id="fullName" name="fullName" placeholder="أدخل اسمك الكامل">
                </div>
                <div class="form-group">
                    <label for="country">البلد:</label>
                    <select id="country" name="country">
                        <option value="">اختر البلد</option>
                        <option value="sa">السعودية</option>
                        <option value="ae">الإمارات</option>
                        <option value="eg">مصر</option>
                        <option value="jo">الأردن</option>
                        <option value="lb">لبنان</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="message">رسالة:</label>
                    <textarea id="message" name="message" rows="4" placeholder="اكتب رسالتك هنا..."></textarea>
                </div>
                <div class="form-group">
                    <label for="priority">الأولوية:</label>
                    <select id="priority" name="priority">
                        <option value="low">منخفضة</option>
                        <option value="medium" selected>متوسطة</option>
                        <option value="high">عالية</option>
                        <option value="urgent">عاجلة</option>
                    </select>
                </div>
                <button type="submit" class="btn-success">إرسال النموذج</button>
            </form>
        </div>

        <!-- قسم الأزرار التفاعلية -->
        <div class="section">
            <h2>🎯 أزرار تفاعلية</h2>
            <div class="button-group">
                <button id="alertBtn" class="btn-primary">عرض تنبيه</button>
                <button id="confirmBtn" class="btn-warning">تأكيد</button>
                <button id="promptBtn" class="btn-secondary">إدخال نص</button>
                <button id="modalBtn" class="btn-success">فتح نافذة</button>
                <button id="hideBtn" class="btn-danger">إخفاء عنصر</button>
                <button id="showBtn" class="btn-primary hidden">إظهار عنصر</button>
                <button id="highlightBtn" class="btn-warning">إبراز النص</button>
                <button id="scrollBtn" class="btn-secondary">تمرير لأسفل</button>
            </div>
            <div id="result" class="result">
                <strong>النتيجة:</strong> <span id="resultText"></span>
            </div>
        </div>

        <!-- قسم البيانات -->
        <div class="section">
            <h2>📊 جدول البيانات</h2>
            <table class="data-table" id="dataTable">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>أحمد محمد</td>
                        <td><EMAIL></td>
                        <td>نشط</td>
                        <td><button class="btn-primary" onclick="editRow(1)">تعديل</button></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>فاطمة علي</td>
                        <td><EMAIL></td>
                        <td>غير نشط</td>
                        <td><button class="btn-primary" onclick="editRow(2)">تعديل</button></td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>محمد حسن</td>
                        <td><EMAIL></td>
                        <td>نشط</td>
                        <td><button class="btn-primary" onclick="editRow(3)">تعديل</button></td>
                    </tr>
                </tbody>
            </table>
            <button id="addRowBtn" class="btn-success" style="margin-top: 15px;">إضافة صف جديد</button>
        </div>

        <!-- قسم البحث والتصفية -->
        <div class="section">
            <h2>🔍 البحث والتصفية</h2>
            <div class="form-group">
                <label for="searchInput">البحث:</label>
                <input type="text" id="searchInput" placeholder="ابحث في الجدول...">
            </div>
            <div class="form-group">
                <label for="statusFilter">تصفية حسب الحالة:</label>
                <select id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="نشط">نشط</option>
                    <option value="غير نشط">غير نشط</option>
                </select>
            </div>
            <button id="searchBtn" class="btn-primary">بحث</button>
            <button id="clearBtn" class="btn-secondary">مسح</button>
        </div>

        <!-- قسم التحميل والانتظار -->
        <div class="section">
            <h2>⏳ التحميل والانتظار</h2>
            <button id="loadBtn" class="btn-primary">بدء التحميل</button>
            <button id="slowBtn" class="btn-warning">عملية بطيئة (5 ثواني)</button>
            <div id="loadingIndicator" class="hidden" style="margin-top: 15px;">
                <p>⏳ جاري التحميل...</p>
                <div style="width: 100%; background: #f0f0f0; border-radius: 10px; overflow: hidden;">
                    <div id="progressBar" style="width: 0%; height: 20px; background: #667eea; transition: width 0.3s ease;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- النافذة المنبثقة -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal()">&times;</span>
            <h3>نافذة منبثقة</h3>
            <p>هذه نافذة منبثقة لاختبار التفاعل مع العناصر المخفية.</p>
            <div class="form-group">
                <label for="modalInput">أدخل نص:</label>
                <input type="text" id="modalInput" placeholder="اكتب شيئاً هنا...">
            </div>
            <button onclick="closeModal()" class="btn-primary">إغلاق</button>
        </div>
    </div>

    <script>
        // معالجات الأحداث
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            showResult('تم تسجيل الدخول بنجاح!');
        });

        document.getElementById('advancedForm').addEventListener('submit', function(e) {
            e.preventDefault();
            showResult('تم إرسال النموذج بنجاح!');
        });

        document.getElementById('alertBtn').addEventListener('click', function() {
            alert('هذا تنبيه من WebFlow Automator Pro!');
        });

        document.getElementById('confirmBtn').addEventListener('click', function() {
            if (confirm('هل أنت متأكد؟')) {
                showResult('تم التأكيد');
            } else {
                showResult('تم الإلغاء');
            }
        });

        document.getElementById('promptBtn').addEventListener('click', function() {
            const input = prompt('أدخل نص:');
            if (input) {
                showResult('تم إدخال: ' + input);
            }
        });

        document.getElementById('modalBtn').addEventListener('click', function() {
            document.getElementById('modal').style.display = 'flex';
        });

        document.getElementById('hideBtn').addEventListener('click', function() {
            this.classList.add('hidden');
            document.getElementById('showBtn').classList.remove('hidden');
            showResult('تم إخفاء الزر');
        });

        document.getElementById('showBtn').addEventListener('click', function() {
            this.classList.add('hidden');
            document.getElementById('hideBtn').classList.remove('hidden');
            showResult('تم إظهار الزر');
        });

        document.getElementById('highlightBtn').addEventListener('click', function() {
            document.querySelector('h1').classList.toggle('highlight');
            showResult('تم إبراز العنوان');
        });

        document.getElementById('scrollBtn').addEventListener('click', function() {
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            showResult('تم التمرير لأسفل');
        });

        document.getElementById('loadBtn').addEventListener('click', function() {
            simulateLoading(2000);
        });

        document.getElementById('slowBtn').addEventListener('click', function() {
            simulateLoading(5000);
        });

        document.getElementById('searchBtn').addEventListener('click', function() {
            const searchTerm = document.getElementById('searchInput').value;
            const statusFilter = document.getElementById('statusFilter').value;
            showResult(`البحث عن: "${searchTerm}" مع تصفية: "${statusFilter}"`);
        });

        document.getElementById('clearBtn').addEventListener('click', function() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            showResult('تم مسح البحث');
        });

        document.getElementById('addRowBtn').addEventListener('click', function() {
            const table = document.getElementById('dataTable').getElementsByTagName('tbody')[0];
            const newRow = table.insertRow();
            const rowCount = table.rows.length;
            
            newRow.innerHTML = `
                <td>${rowCount}</td>
                <td>مستخدم جديد ${rowCount}</td>
                <td>user${rowCount}@example.com</td>
                <td>نشط</td>
                <td><button class="btn-primary" onclick="editRow(${rowCount})">تعديل</button></td>
            `;
            
            showResult('تم إضافة صف جديد');
        });

        function showResult(text) {
            const result = document.getElementById('result');
            const resultText = document.getElementById('resultText');
            resultText.textContent = text;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 3000);
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        function editRow(rowNumber) {
            showResult(`تم النقر على تعديل الصف رقم ${rowNumber}`);
        }

        function simulateLoading(duration) {
            const indicator = document.getElementById('loadingIndicator');
            const progressBar = document.getElementById('progressBar');
            
            indicator.classList.remove('hidden');
            progressBar.style.width = '0%';
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 100 / (duration / 100);
                progressBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        indicator.classList.add('hidden');
                        showResult('تم التحميل بنجاح!');
                    }, 500);
                }
            }, 100);
        }

        // إضافة بعض التفاعلات الإضافية
        document.addEventListener('DOMContentLoaded', function() {
            console.log('صفحة اختبار WebFlow Automator Pro جاهزة!');
            
            // إضافة تأثيرات hover للجدول
            const tableRows = document.querySelectorAll('.data-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#e3f2fd';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
        });
    </script>
</body>
</html>
