/**
 * WebFlow Automator Pro - Smart Element Selector
 * Advanced element selection and CSS selector generation
 */

class ElementSelector {
  constructor() {
    this.isSelecting = false;
    this.selectedElement = null;
    this.overlay = null;
    this.highlightBox = null;
    this.onElementSelected = null;
    this.excludeSelectors = [
      '.webflow-selector-overlay',
      '.webflow-highlight-box',
      '.webflow-selector-tooltip'
    ];
    
    this.init();
  }

  init() {
    this.createOverlay();
    this.createHighlightBox();
    this.bindEvents();
  }

  createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.className = 'webflow-selector-overlay';
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
      z-index: 999999;
      cursor: crosshair;
      display: none;
      backdrop-filter: blur(2px);
    `;
    document.body.appendChild(this.overlay);
  }

  createHighlightBox() {
    this.highlightBox = document.createElement('div');
    this.highlightBox.className = 'webflow-highlight-box';
    this.highlightBox.style.cssText = `
      position: absolute;
      border: 3px solid #ff6b6b;
      background: rgba(255, 107, 107, 0.1);
      pointer-events: none;
      z-index: 1000000;
      display: none;
      border-radius: 4px;
      box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
      transition: all 0.1s ease;
    `;
    
    // Add tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'webflow-selector-tooltip';
    tooltip.style.cssText = `
      position: absolute;
      top: -35px;
      left: 0;
      background: #333;
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      font-family: Arial, sans-serif;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    `;
    
    this.highlightBox.appendChild(tooltip);
    document.body.appendChild(this.highlightBox);
  }

  bindEvents() {
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleClick = this.handleClick.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
  }

  startSelection(callback) {
    if (this.isSelecting) return;
    
    this.isSelecting = true;
    this.onElementSelected = callback;
    this.overlay.style.display = 'block';
    
    document.addEventListener('mousemove', this.handleMouseMove);
    document.addEventListener('click', this.handleClick, true);
    document.addEventListener('keydown', this.handleKeyDown);
    
    // Disable page scrolling
    document.body.style.overflow = 'hidden';
    
    this.showInstructions();
  }

  stopSelection() {
    if (!this.isSelecting) return;
    
    this.isSelecting = false;
    this.overlay.style.display = 'none';
    this.highlightBox.style.display = 'none';
    
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('click', this.handleClick, true);
    document.removeEventListener('keydown', this.handleKeyDown);
    
    // Re-enable page scrolling
    document.body.style.overflow = '';
    
    this.hideInstructions();
  }

  handleMouseMove(event) {
    if (!this.isSelecting) return;
    
    const element = this.getElementFromPoint(event.clientX, event.clientY);
    if (element && !this.isExcludedElement(element)) {
      this.highlightElement(element);
    }
  }

  handleClick(event) {
    if (!this.isSelecting) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    const element = this.getElementFromPoint(event.clientX, event.clientY);
    if (element && !this.isExcludedElement(element)) {
      this.selectElement(element);
    }
  }

  handleKeyDown(event) {
    if (!this.isSelecting) return;
    
    if (event.key === 'Escape') {
      event.preventDefault();
      this.stopSelection();
      if (this.onElementSelected) {
        this.onElementSelected(null);
      }
    }
  }

  getElementFromPoint(x, y) {
    // Temporarily hide overlay to get element underneath
    this.overlay.style.display = 'none';
    this.highlightBox.style.display = 'none';
    
    const element = document.elementFromPoint(x, y);
    
    this.overlay.style.display = 'block';
    
    return element;
  }

  isExcludedElement(element) {
    return this.excludeSelectors.some(selector => {
      try {
        return element.matches(selector) || element.closest(selector);
      } catch (e) {
        return false;
      }
    });
  }

  highlightElement(element) {
    const rect = element.getBoundingClientRect();
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;
    
    this.highlightBox.style.display = 'block';
    this.highlightBox.style.left = (rect.left + scrollX) + 'px';
    this.highlightBox.style.top = (rect.top + scrollY) + 'px';
    this.highlightBox.style.width = rect.width + 'px';
    this.highlightBox.style.height = rect.height + 'px';
    
    // Update tooltip
    const tooltip = this.highlightBox.querySelector('.webflow-selector-tooltip');
    const elementInfo = this.getElementInfo(element);
    tooltip.textContent = elementInfo;
  }

  selectElement(element) {
    this.selectedElement = element;
    const selector = this.generateOptimalSelector(element);
    
    this.stopSelection();
    
    if (this.onElementSelected) {
      this.onElementSelected({
        element: element,
        selector: selector,
        info: this.getElementInfo(element),
        rect: element.getBoundingClientRect()
      });
    }
  }

  getElementInfo(element) {
    const tagName = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const className = element.className ? `.${element.className.split(' ').join('.')}` : '';
    const text = element.textContent ? element.textContent.trim().substring(0, 20) : '';
    
    return `${tagName}${id}${className} ${text ? `"${text}..."` : ''}`.trim();
  }

  generateOptimalSelector(element) {
    // Try different selector strategies in order of preference
    const strategies = [
      () => this.getSelectorById(element),
      () => this.getSelectorByUniqueAttribute(element),
      () => this.getSelectorByClass(element),
      () => this.getSelectorByTagAndPosition(element),
      () => this.getSelectorByPath(element)
    ];

    for (const strategy of strategies) {
      const selector = strategy();
      if (selector && this.isUniqueSelector(selector)) {
        return selector;
      }
    }

    // Fallback to xpath if CSS selector fails
    return this.generateXPathSelector(element);
  }

  getSelectorById(element) {
    if (element.id && /^[a-zA-Z][\w-]*$/.test(element.id)) {
      return `#${element.id}`;
    }
    return null;
  }

  getSelectorByUniqueAttribute(element) {
    const uniqueAttributes = ['data-testid', 'data-id', 'name', 'aria-label'];
    
    for (const attr of uniqueAttributes) {
      const value = element.getAttribute(attr);
      if (value) {
        const selector = `[${attr}="${value}"]`;
        if (this.isUniqueSelector(selector)) {
          return selector;
        }
      }
    }
    return null;
  }

  getSelectorByClass(element) {
    if (!element.className) return null;
    
    const classes = element.className.split(' ').filter(c => c && !/^(active|selected|hover|focus)$/i.test(c));
    if (classes.length === 0) return null;
    
    // Try single classes first
    for (const cls of classes) {
      const selector = `.${cls}`;
      if (this.isUniqueSelector(selector)) {
        return selector;
      }
    }
    
    // Try combination of classes
    const selector = `.${classes.join('.')}`;
    if (this.isUniqueSelector(selector)) {
      return selector;
    }
    
    return null;
  }

  getSelectorByTagAndPosition(element) {
    const tagName = element.tagName.toLowerCase();
    const parent = element.parentElement;
    
    if (!parent) return tagName;
    
    const siblings = Array.from(parent.children).filter(el => el.tagName.toLowerCase() === tagName);
    const index = siblings.indexOf(element);
    
    if (siblings.length === 1) {
      return `${this.generateOptimalSelector(parent)} > ${tagName}`;
    } else {
      return `${this.generateOptimalSelector(parent)} > ${tagName}:nth-child(${index + 1})`;
    }
  }

  getSelectorByPath(element) {
    const path = [];
    let current = element;
    
    while (current && current !== document.body) {
      let selector = current.tagName.toLowerCase();
      
      if (current.id) {
        selector += `#${current.id}`;
        path.unshift(selector);
        break;
      }
      
      if (current.className) {
        const classes = current.className.split(' ').filter(c => c);
        if (classes.length > 0) {
          selector += `.${classes[0]}`;
        }
      }
      
      const parent = current.parentElement;
      if (parent) {
        const siblings = Array.from(parent.children).filter(el => el.tagName === current.tagName);
        if (siblings.length > 1) {
          const index = siblings.indexOf(current);
          selector += `:nth-child(${index + 1})`;
        }
      }
      
      path.unshift(selector);
      current = parent;
    }
    
    return path.join(' > ');
  }

  generateXPathSelector(element) {
    const path = [];
    let current = element;
    
    while (current && current !== document.documentElement) {
      let selector = current.tagName.toLowerCase();
      
      if (current.id) {
        return `//*[@id="${current.id}"]`;
      }
      
      const parent = current.parentElement;
      if (parent) {
        const siblings = Array.from(parent.children).filter(el => el.tagName === current.tagName);
        if (siblings.length > 1) {
          const index = siblings.indexOf(current) + 1;
          selector += `[${index}]`;
        }
      }
      
      path.unshift(selector);
      current = parent;
    }
    
    return '//' + path.join('/');
  }

  isUniqueSelector(selector) {
    try {
      const elements = document.querySelectorAll(selector);
      return elements.length === 1;
    } catch (e) {
      return false;
    }
  }

  showInstructions() {
    const instructions = document.createElement('div');
    instructions.className = 'webflow-instructions';
    instructions.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: #333;
      color: white;
      padding: 15px 25px;
      border-radius: 8px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 1000001;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      text-align: center;
      direction: rtl;
    `;
    instructions.innerHTML = `
      <div>انقر على العنصر الذي تريد تحديده</div>
      <div style="font-size: 12px; margin-top: 5px; opacity: 0.8;">اضغط ESC للإلغاء</div>
    `;
    
    document.body.appendChild(instructions);
    
    setTimeout(() => {
      if (instructions.parentNode) {
        instructions.remove();
      }
    }, 3000);
  }

  hideInstructions() {
    const instructions = document.querySelector('.webflow-instructions');
    if (instructions) {
      instructions.remove();
    }
  }

  destroy() {
    this.stopSelection();
    
    if (this.overlay && this.overlay.parentNode) {
      this.overlay.remove();
    }
    
    if (this.highlightBox && this.highlightBox.parentNode) {
      this.highlightBox.remove();
    }
  }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
  window.ElementSelector = ElementSelector;
}
