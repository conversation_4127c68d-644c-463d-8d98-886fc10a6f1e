<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="60" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Automation Gear -->
  <g transform="translate(64,64)">
    <!-- Gear Teeth -->
    <g fill="#ffffff" opacity="0.9">
      <rect x="-2" y="-28" width="4" height="8" rx="2"/>
      <rect x="-2" y="20" width="4" height="8" rx="2"/>
      <rect x="-28" y="-2" width="8" height="4" rx="2"/>
      <rect x="20" y="-2" width="8" height="4" rx="2"/>
      
      <rect x="-20" y="-20" width="4" height="6" rx="2" transform="rotate(45)"/>
      <rect x="16" y="-20" width="4" height="6" rx="2" transform="rotate(45)"/>
      <rect x="-20" y="14" width="4" height="6" rx="2" transform="rotate(45)"/>
      <rect x="16" y="14" width="4" height="6" rx="2" transform="rotate(45)"/>
    </g>
    
    <!-- Main Gear Circle -->
    <circle cx="0" cy="0" r="18" fill="#ffffff" opacity="0.95"/>
    
    <!-- Inner Circle -->
    <circle cx="0" cy="0" r="8" fill="url(#bgGradient)"/>
    
    <!-- Flow Arrows -->
    <g fill="#ffffff" opacity="0.8">
      <!-- Right Arrow -->
      <path d="M 25 -4 L 35 0 L 25 4 Z"/>
      <!-- Left Arrow -->
      <path d="M -25 -4 L -35 0 L -25 4 Z"/>
      <!-- Top Arrow -->
      <path d="M -4 -25 L 0 -35 L 4 -25 Z"/>
      <!-- Bottom Arrow -->
      <path d="M -4 25 L 0 35 L 4 25 Z"/>
    </g>
  </g>
  
  <!-- WebFlow Text (Optional) -->
  <text x="64" y="110" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold" opacity="0.8">WebFlow</text>
</svg>
