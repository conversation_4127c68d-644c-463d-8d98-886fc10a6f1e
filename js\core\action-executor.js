/**
 * WebFlow Automator Pro - Advanced Action Executor
 * Handles execution of automation actions with error handling and reporting
 */

class ActionExecutor {
  constructor() {
    this.isExecuting = false;
    this.currentExecution = null;
    this.executionLog = [];
    this.variables = new Map();
    this.settings = {
      speed: 'normal',
      errorHandling: 'continue',
      highlightElements: true,
      logActions: true
    };
    
    this.speedSettings = {
      slow: 2000,
      normal: 1000,
      fast: 500,
      instant: 0
    };
  }

  /**
   * Execute a sequence of actions
   * @param {Array} actions - Array of action objects
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} Execution result
   */
  async executeActions(actions, options = {}) {
    if (this.isExecuting) {
      throw new Error('Another execution is already in progress');
    }

    this.isExecuting = true;
    this.currentExecution = {
      id: StringUtils.generateId(),
      startTime: Date.now(),
      actions: actions,
      options: { ...this.settings, ...options },
      status: 'running',
      currentActionIndex: 0,
      results: [],
      errors: []
    };

    try {
      const result = await this.runActionSequence(actions, this.currentExecution.options);
      
      this.currentExecution.status = 'completed';
      this.currentExecution.endTime = Date.now();
      this.currentExecution.duration = this.currentExecution.endTime - this.currentExecution.startTime;
      
      this.logExecution(this.currentExecution);
      
      return {
        success: true,
        execution: this.currentExecution,
        results: result
      };
      
    } catch (error) {
      this.currentExecution.status = 'failed';
      this.currentExecution.error = error.message;
      this.currentExecution.endTime = Date.now();
      
      this.logExecution(this.currentExecution);
      
      return {
        success: false,
        execution: this.currentExecution,
        error: error.message
      };
      
    } finally {
      this.isExecuting = false;
      this.currentExecution = null;
    }
  }

  /**
   * Run sequence of actions
   * @param {Array} actions
   * @param {Object} options
   * @returns {Promise<Array>}
   */
  async runActionSequence(actions, options) {
    const results = [];
    
    for (let i = 0; i < actions.length; i++) {
      if (this.currentExecution) {
        this.currentExecution.currentActionIndex = i;
      }
      
      try {
        const action = actions[i];
        const result = await this.executeAction(action, options);
        results.push(result);
        
        if (this.currentExecution) {
          this.currentExecution.results.push(result);
        }
        
        // Add delay between actions based on speed setting
        const delay = this.speedSettings[options.speed] || this.speedSettings.normal;
        if (delay > 0) {
          await this.wait(delay);
        }
        
      } catch (error) {
        const actionError = {
          actionIndex: i,
          action: actions[i],
          error: error.message,
          timestamp: Date.now()
        };
        
        if (this.currentExecution) {
          this.currentExecution.errors.push(actionError);
        }
        
        if (options.errorHandling === 'stop') {
          throw error;
        } else if (options.errorHandling === 'continue') {
          console.warn(`Action ${i} failed:`, error.message);
          results.push({ success: false, error: error.message });
        }
      }
    }
    
    return results;
  }

  /**
   * Execute a single action
   * @param {Object} action
   * @param {Object} options
   * @returns {Promise<Object>}
   */
  async executeAction(action, options = {}) {
    const startTime = Date.now();
    
    try {
      // Resolve variables in action
      const resolvedAction = this.resolveVariables(action);
      
      // Wait before action if specified
      if (resolvedAction.waitBefore > 0) {
        await this.wait(resolvedAction.waitBefore);
      }
      
      // Find target element
      const element = await this.findElement(resolvedAction.selector, resolvedAction.timeout || 10000);
      
      // Highlight element if enabled
      if (options.highlightElements && element) {
        DOMUtils.highlightElement(element);
      }
      
      // Scroll element into view
      if (element) {
        DOMUtils.scrollIntoView(element);
        await this.wait(200); // Small delay for scroll
      }
      
      // Execute the action
      let result;
      switch (resolvedAction.type) {
        case 'click':
          result = await this.executeClick(element, resolvedAction);
          break;
        case 'type':
          result = await this.executeType(element, resolvedAction);
          break;
        case 'select':
          result = await this.executeSelect(element, resolvedAction);
          break;
        case 'wait':
          result = await this.executeWait(resolvedAction);
          break;
        case 'scroll':
          result = await this.executeScroll(resolvedAction);
          break;
        case 'hover':
          result = await this.executeHover(element, resolvedAction);
          break;
        case 'extract':
          result = await this.executeExtract(element, resolvedAction);
          break;
        case 'condition':
          result = await this.executeCondition(resolvedAction);
          break;
        case 'loop':
          result = await this.executeLoop(resolvedAction);
          break;
        case 'variable':
          result = await this.executeVariable(resolvedAction);
          break;
        case 'javascript':
          result = await this.executeJavaScript(resolvedAction);
          break;
        default:
          throw new Error(`Unknown action type: ${resolvedAction.type}`);
      }
      
      // Wait after action if specified
      if (resolvedAction.waitAfter > 0) {
        await this.wait(resolvedAction.waitAfter);
      }
      
      const endTime = Date.now();
      
      return {
        success: true,
        action: resolvedAction,
        result: result,
        duration: endTime - startTime,
        timestamp: startTime
      };
      
    } catch (error) {
      const endTime = Date.now();
      
      return {
        success: false,
        action: action,
        error: error.message,
        duration: endTime - startTime,
        timestamp: startTime
      };
    }
  }

  /**
   * Find element with timeout
   * @param {string} selector
   * @param {number} timeout
   * @returns {Promise<Element>}
   */
  async findElement(selector, timeout = 10000) {
    if (!selector) {
      throw new Error('Selector is required');
    }
    
    // Handle XPath selectors
    if (selector.startsWith('//') || selector.startsWith('/')) {
      return this.findElementByXPath(selector, timeout);
    }
    
    // Handle CSS selectors
    return DOMUtils.waitForElement(selector, timeout);
  }

  /**
   * Find element by XPath
   * @param {string} xpath
   * @param {number} timeout
   * @returns {Promise<Element>}
   */
  async findElementByXPath(xpath, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkElement = () => {
        try {
          const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
          const element = result.singleNodeValue;
          
          if (element) {
            resolve(element);
            return;
          }
          
          if (Date.now() - startTime > timeout) {
            reject(new Error(`Element not found by XPath: ${xpath}`));
            return;
          }
          
          setTimeout(checkElement, 100);
        } catch (error) {
          reject(new Error(`Invalid XPath: ${xpath}`));
        }
      };
      
      checkElement();
    });
  }

  /**
   * Execute click action
   */
  async executeClick(element, action) {
    if (!element) {
      throw new Error('Element not found for click action');
    }
    
    // Ensure element is visible and clickable
    if (!DOMUtils.isElementVisible(element)) {
      throw new Error('Element is not visible');
    }
    
    // Handle different click types
    const clickType = action.clickType || 'left';
    
    switch (clickType) {
      case 'left':
        element.click();
        break;
      case 'right':
        element.dispatchEvent(new MouseEvent('contextmenu', { bubbles: true }));
        break;
      case 'double':
        element.dispatchEvent(new MouseEvent('dblclick', { bubbles: true }));
        break;
      case 'middle':
        element.dispatchEvent(new MouseEvent('click', { button: 1, bubbles: true }));
        break;
    }
    
    return { clicked: true, clickType: clickType };
  }

  /**
   * Execute type action
   */
  async executeType(element, action) {
    if (!element) {
      throw new Error('Element not found for type action');
    }
    
    const text = action.text || action.value || '';
    const clearFirst = action.clearFirst !== false;
    const typeSpeed = action.typeSpeed || 50;
    
    // Clear existing text if requested
    if (clearFirst) {
      element.value = '';
      element.textContent = '';
    }
    
    // Focus the element
    element.focus();
    
    // Type text character by character if speed is specified
    if (typeSpeed > 0 && text.length > 1) {
      for (const char of text) {
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
          element.value += char;
        } else {
          element.textContent += char;
        }
        
        // Trigger input event
        element.dispatchEvent(new Event('input', { bubbles: true }));
        
        await this.wait(typeSpeed);
      }
    } else {
      // Type all at once
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        element.value = text;
      } else {
        element.textContent = text;
      }
      
      element.dispatchEvent(new Event('input', { bubbles: true }));
    }
    
    // Trigger change event
    element.dispatchEvent(new Event('change', { bubbles: true }));
    
    return { typed: text, length: text.length };
  }

  /**
   * Execute select action
   */
  async executeSelect(element, action) {
    if (!element || element.tagName !== 'SELECT') {
      throw new Error('Element is not a select element');
    }
    
    const value = action.value;
    const text = action.text;
    
    let option = null;
    
    if (value) {
      option = element.querySelector(`option[value="${value}"]`);
    } else if (text) {
      option = Array.from(element.options).find(opt => opt.textContent.trim() === text);
    }
    
    if (!option) {
      throw new Error(`Option not found: ${value || text}`);
    }
    
    option.selected = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    
    return { selected: option.value, text: option.textContent };
  }

  /**
   * Execute wait action
   */
  async executeWait(action) {
    const duration = action.duration || action.time || 1000;
    await this.wait(duration);
    return { waited: duration };
  }

  /**
   * Execute scroll action
   */
  async executeScroll(action) {
    const direction = action.direction || 'down';
    const amount = action.amount || 500;
    
    switch (direction) {
      case 'up':
        window.scrollBy(0, -amount);
        break;
      case 'down':
        window.scrollBy(0, amount);
        break;
      case 'left':
        window.scrollBy(-amount, 0);
        break;
      case 'right':
        window.scrollBy(amount, 0);
        break;
      case 'top':
        window.scrollTo(0, 0);
        break;
      case 'bottom':
        window.scrollTo(0, document.body.scrollHeight);
        break;
    }
    
    return { scrolled: direction, amount: amount };
  }

  /**
   * Execute hover action
   */
  async executeHover(element, action) {
    if (!element) {
      throw new Error('Element not found for hover action');
    }
    
    element.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));
    element.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }));
    
    const duration = action.duration || 1000;
    await this.wait(duration);
    
    return { hovered: true, duration: duration };
  }

  /**
   * Execute extract action
   */
  async executeExtract(element, action) {
    if (!element) {
      throw new Error('Element not found for extract action');
    }
    
    const attribute = action.attribute;
    let value;
    
    if (attribute) {
      value = element.getAttribute(attribute);
    } else {
      value = element.textContent || element.value || element.innerHTML;
    }
    
    // Store in variable if specified
    if (action.variable) {
      this.variables.set(action.variable, value);
    }
    
    return { extracted: value, variable: action.variable };
  }

  /**
   * Execute JavaScript action
   */
  async executeJavaScript(action) {
    const code = action.code;
    if (!code) {
      throw new Error('JavaScript code is required');
    }
    
    try {
      // Create a safe execution context
      const result = new Function('variables', 'DOMUtils', 'document', 'window', code)(
        Object.fromEntries(this.variables),
        DOMUtils,
        document,
        window
      );
      
      return { executed: true, result: result };
    } catch (error) {
      throw new Error(`JavaScript execution failed: ${error.message}`);
    }
  }

  /**
   * Execute variable action
   */
  async executeVariable(action) {
    const name = action.name;
    const value = action.value;
    
    if (!name) {
      throw new Error('Variable name is required');
    }
    
    this.variables.set(name, value);
    
    return { variable: name, value: value };
  }

  /**
   * Resolve variables in action
   */
  resolveVariables(action) {
    const resolved = { ...action };
    
    // Replace variables in string values
    for (const [key, value] of Object.entries(resolved)) {
      if (typeof value === 'string') {
        resolved[key] = this.replaceVariables(value);
      }
    }
    
    return resolved;
  }

  /**
   * Replace variables in string
   */
  replaceVariables(str) {
    return str.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
      return this.variables.get(varName) || match;
    });
  }

  /**
   * Wait for specified duration
   */
  async wait(duration) {
    return new Promise(resolve => setTimeout(resolve, duration));
  }

  /**
   * Stop current execution
   */
  stopExecution() {
    if (this.currentExecution) {
      this.currentExecution.status = 'stopped';
      this.currentExecution.endTime = Date.now();
    }
    
    this.isExecuting = false;
  }

  /**
   * Log execution for reporting
   */
  logExecution(execution) {
    this.executionLog.push(execution);
    
    // Keep only last 100 executions
    if (this.executionLog.length > 100) {
      this.executionLog = this.executionLog.slice(-100);
    }
    
    // Store in chrome storage
    StorageUtils.set({ executionLog: this.executionLog });
  }

  /**
   * Get execution statistics
   */
  getExecutionStats() {
    const total = this.executionLog.length;
    const successful = this.executionLog.filter(e => e.status === 'completed').length;
    const failed = this.executionLog.filter(e => e.status === 'failed').length;
    const avgDuration = this.executionLog.reduce((sum, e) => sum + (e.duration || 0), 0) / total;
    
    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total * 100).toFixed(1) : 0,
      avgDuration: Math.round(avgDuration)
    };
  }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
  window.ActionExecutor = ActionExecutor;
}
