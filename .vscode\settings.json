{"editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": true}, "files.encoding": "utf8", "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.associations": {"*.json": "jsonc"}, "emmet.includeLanguages": {"javascript": "javascriptreact"}, "javascript.preferences.quoteStyle": "single", "typescript.preferences.quoteStyle": "single", "html.format.wrapAttributes": "force-aligned", "css.format.spaceAroundSelectorSeparator": true, "json.format.enable": true, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/*.zip": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/*.zip": true}, "editor.rulers": [80, 120], "editor.wordWrap": "on", "editor.minimap.enabled": false, "breadcrumbs.enabled": true, "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "workbench.editor.enablePreview": false, "extensions.recommendations": ["ms-vscode.vscode-json", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-next"]}