<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg32" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
  </defs>
  <circle cx="16" cy="16" r="14" fill="url(#bg32)"/>
  <circle cx="16" cy="16" r="8" fill="none" stroke="white" stroke-width="2"/>
  <rect x="15" y="4" width="2" height="3" fill="white"/>
  <rect x="15" y="25" width="2" height="3" fill="white"/>
  <rect x="4" y="15" width="3" height="2" fill="white"/>
  <rect x="25" y="15" width="3" height="2" fill="white"/>
  <rect x="23" y="7" width="2" height="3" fill="white" transform="rotate(45 24 8.5)"/>
  <rect x="7" y="22" width="2" height="3" fill="white" transform="rotate(45 8 23.5)"/>
  <rect x="7" y="7" width="2" height="3" fill="white" transform="rotate(-45 8 8.5)"/>
  <rect x="23" y="22" width="2" height="3" fill="white" transform="rotate(-45 24 23.5)"/>
  <circle cx="16" cy="16" r="3" fill="white"/>
</svg>
