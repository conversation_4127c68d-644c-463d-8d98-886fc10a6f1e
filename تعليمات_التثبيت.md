# 🚀 دليل تثبيت أداة الأتمتة الذكية

## 📋 خطوات التثبيت السريع

### الخطوة 1: تحضير الملفات
1. تأكد من وجود جميع الملفات في مجلد واحد:
   ```
   📁 أداة-الأتمتة-الذكية/
   ├── 📄 manifest.json
   ├── 🎨 popup.html
   ├── 🎨 popup.css
   ├── 📜 popup.js
   ├── 📜 content.js
   ├── 📜 content.css
   ├── 📜 background.js
   └── 📁 icons/
       ├── icon16.svg
       ├── icon32.svg
       ├── icon48.svg
       └── icon128.svg
   ```

### الخطوة 2: فتح Chrome Extensions
1. افتح متصفح Google Chrome
2. اكتب في شريط العنوان: `chrome://extensions/`
3. اضغط Enter

### الخطوة 3: تفعيل وضع المطور
1. في الزاوية العلوية اليمنى، فعل "وضع المطور" (Developer mode)
2. ستظهر أزرار جديدة في الأعلى

### الخطوة 4: تحميل الإضافة
1. انقر على "تحميل إضافة غير مضغوطة" (Load unpacked)
2. اختر مجلد الإضافة الذي يحتوي على ملف `manifest.json`
3. انقر على "اختيار مجلد" أو "Select Folder"

### الخطوة 5: التحقق من التثبيت
1. ستظهر الإضافة في قائمة الإضافات
2. ستظهر أيقونة الإضافة في شريط الأدوات
3. انقر على الأيقونة للتأكد من فتح الواجهة

## 🎯 كيفية الاستخدام

### تسجيل الإجراءات
1. انقر على أيقونة الإضافة
2. انقر على "بدء التسجيل"
3. قم بالإجراءات المطلوبة على الموقع
4. انقر على "إيقاف التسجيل"

### إضافة إجراء يدوي
1. اختر نوع الإجراء من القائمة
2. انقر على 🎯 لتحديد العنصر من الصفحة
3. أدخل القيمة المطلوبة (إن وجدت)
4. انقر على "إضافة الإجراء"

### تنفيذ الإجراءات
1. اذهب إلى قسم "قائمة الإجراءات"
2. اختر سرعة التنفيذ
3. انقر على "تنفيذ الإجراءات"

## 🔧 أنواع الإجراءات المدعومة

| النوع | الوصف | مثال |
|-------|--------|-------|
| 👆 نقر | النقر على عنصر | النقر على زر "تسجيل الدخول" |
| ⌨️ كتابة | إدخال نص في حقل | كتابة البريد الإلكتروني |
| ⏰ انتظار | توقف لفترة محددة | انتظار تحميل الصفحة |
| 📜 تمرير | تمرير الصفحة | التمرير لأسفل أو لأعلى |

## 🛠️ استكشاف الأخطاء

### المشكلة: الإضافة لا تظهر
**الحل:**
- تأكد من تفعيل "وضع المطور"
- تحقق من وجود ملف `manifest.json`
- أعد تحميل الإضافة

### المشكلة: لا يمكن تحديد العناصر
**الحل:**
- تأكد من تحميل الصفحة بالكامل
- جرب إعادة تحميل الصفحة
- تحقق من عدم وجود أخطاء في وحدة التحكم

### المشكلة: الإجراءات لا تعمل
**الحل:**
- تحقق من صحة محددات العناصر
- زد وقت الانتظار بين الإجراءات
- تأكد من أن العناصر مرئية في الصفحة

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:
1. تحقق من وحدة التحكم في Chrome (F12)
2. ابحث عن رسائل الخطأ
3. تأكد من أن الموقع يسمح بتشغيل الإضافات

## 🔒 الأمان والخصوصية

- جميع البيانات تُحفظ محلياً في متصفحك
- لا نرسل أي معلومات إلى خوادم خارجية
- الإضافة تعمل فقط عند تفعيلها

## 🎉 نصائح للاستخدام الأمثل

1. **ابدأ بإجراءات بسيطة** - جرب النقر والكتابة أولاً
2. **استخدم أوقات انتظار مناسبة** - خاصة للمواقع البطيئة
3. **احفظ إجراءاتك** - الإضافة تحفظ تلقائياً
4. **اختبر قبل التنفيذ** - استخدم زر "اختبار" للإجراءات اليدوية

## 📈 ميزات متقدمة

### تخصيص السرعة
- **بطيء**: للمواقع البطيئة أو المعقدة
- **عادي**: للاستخدام العام
- **سريع**: للمواقع السريعة والبسيطة

### محددات العناصر
الإضافة تدعم:
- CSS Selectors (مثل: `#button`, `.class`)
- Element IDs (مثل: `#login-button`)
- Class Names (مثل: `.submit-btn`)

---

**🎯 نصيحة:** ابدأ بتجربة الإضافة على مواقع بسيطة قبل استخدامها على مواقع معقدة!

**✨ استمتع بأتمتة مهامك!** 🚀
