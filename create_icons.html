<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات WebFlow Automator Pro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #667eea;
            text-align: center;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .icon-item h3 {
            margin: 10px 0 5px 0;
            color: #333;
        }
        canvas {
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .download-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #5a6fd8;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-right: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 إنشاء أيقونات WebFlow Automator Pro</h1>
        
        <div class="icon-preview" id="iconPreview">
            <!-- سيتم إنشاء الأيقونات هنا -->
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات التثبيت</h3>
            
            <div class="step">
                <strong>الخطوة 1:</strong> انقر على "تحميل" تحت كل أيقونة لحفظها في مجلد icons
            </div>
            
            <div class="step">
                <strong>الخطوة 2:</strong> افتح Google Chrome واذهب إلى: <code>chrome://extensions/</code>
            </div>
            
            <div class="step">
                <strong>الخطوة 3:</strong> فعل "وضع المطور" (Developer mode) في الزاوية العلوية اليمنى
            </div>
            
            <div class="step">
                <strong>الخطوة 4:</strong> انقر على "تحميل إضافة غير مضغوطة" (Load unpacked)
            </div>
            
            <div class="step">
                <strong>الخطوة 5:</strong> اختر مجلد المشروع الذي يحتوي على ملف manifest.json
            </div>
            
            <div class="step">
                <strong>الخطوة 6:</strong> ستظهر الإضافة في قائمة الإضافات وفي شريط الأدوات
            </div>
        </div>
    </div>

    <script>
        // إنشاء الأيقونات
        const sizes = [16, 32, 48, 128];
        const iconPreview = document.getElementById('iconPreview');

        sizes.forEach(size => {
            createIcon(size);
        });

        function createIcon(size) {
            // إنشاء عنصر الأيقونة
            const iconItem = document.createElement('div');
            iconItem.className = 'icon-item';
            
            const title = document.createElement('h3');
            title.textContent = `${size}x${size}`;
            
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'download-btn';
            downloadBtn.textContent = 'تحميل';
            downloadBtn.onclick = () => downloadIcon(canvas, size);
            
            // رسم الأيقونة
            drawIcon(canvas, size);
            
            iconItem.appendChild(title);
            iconItem.appendChild(canvas);
            iconItem.appendChild(downloadBtn);
            iconPreview.appendChild(iconItem);
        }

        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const center = size / 2;
            const radius = size / 2 - 2;
            
            // مسح الخلفية
            ctx.clearRect(0, 0, size, size);
            
            // رسم الخلفية المتدرجة
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // رسم رمز الأتمتة (ترس)
            ctx.fillStyle = 'white';
            ctx.strokeStyle = 'white';
            ctx.lineWidth = Math.max(1, size / 32);
            
            // رسم أسنان الترس
            const gearRadius = size / 4;
            const teethCount = 8;
            
            for (let i = 0; i < teethCount; i++) {
                const angle = (i * 2 * Math.PI) / teethCount;
                const x1 = center + Math.cos(angle) * gearRadius;
                const y1 = center + Math.sin(angle) * gearRadius;
                const x2 = center + Math.cos(angle) * (gearRadius + size / 16);
                const y2 = center + Math.sin(angle) * (gearRadius + size / 16);
                
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            // رسم الدائرة الداخلية
            ctx.beginPath();
            ctx.arc(center, center, gearRadius * 0.6, 0, 2 * Math.PI);
            ctx.fill();
            
            // رسم الدائرة المركزية
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(center, center, gearRadius * 0.3, 0, 2 * Math.PI);
            ctx.fill();
            
            // رسم أسهم التدفق
            if (size >= 32) {
                ctx.fillStyle = 'white';
                ctx.globalAlpha = 0.8;
                
                // سهم يمين
                const arrowSize = size / 16;
                ctx.beginPath();
                ctx.moveTo(center + gearRadius + arrowSize, center - arrowSize/2);
                ctx.lineTo(center + gearRadius + arrowSize * 2, center);
                ctx.lineTo(center + gearRadius + arrowSize, center + arrowSize/2);
                ctx.fill();
                
                // سهم يسار
                ctx.beginPath();
                ctx.moveTo(center - gearRadius - arrowSize, center - arrowSize/2);
                ctx.lineTo(center - gearRadius - arrowSize * 2, center);
                ctx.lineTo(center - gearRadius - arrowSize, center + arrowSize/2);
                ctx.fill();
                
                ctx.globalAlpha = 1;
            }
        }

        function downloadIcon(canvas, size) {
            // تحويل Canvas إلى Blob
            canvas.toBlob(function(blob) {
                // إنشاء رابط التحميل
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }

        // إضافة معلومات إضافية
        document.addEventListener('DOMContentLoaded', function() {
            console.log('مولد أيقونات WebFlow Automator Pro جاهز!');
            
            // إضافة زر تحميل جميع الأيقونات
            const downloadAllBtn = document.createElement('button');
            downloadAllBtn.textContent = '📥 تحميل جميع الأيقونات';
            downloadAllBtn.style.cssText = `
                background: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                margin: 20px auto;
                display: block;
            `;
            
            downloadAllBtn.onclick = function() {
                sizes.forEach((size, index) => {
                    setTimeout(() => {
                        const canvas = document.querySelectorAll('canvas')[index];
                        downloadIcon(canvas, size);
                    }, index * 500); // تأخير بين التحميلات
                });
            };
            
            iconPreview.parentNode.insertBefore(downloadAllBtn, iconPreview.nextSibling);
        });
    </script>
</body>
</html>
