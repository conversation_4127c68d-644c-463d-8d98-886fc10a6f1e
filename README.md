# WebFlow Automator Pro

إضافة Chrome احترافية لأتمتة المواقع مع ميزات متقدمة للتسجيل والتشغيل التلقائي.

## 🌟 الميزات الرئيسية

### 🎯 تسجيل الإجراءات التلقائي
- تسجيل تفاعلات المستخدم تلقائياً (النقر، الكتابة، التمرير)
- محدد عناصر ذكي ومرئي
- دعم CSS Selectors و XPath
- تسجيل بجودة عالية مع معلومات مفصلة

### ⚡ تنفيذ متقدم
- محرك تنفيذ قوي مع معالجة الأخطاء
- سرعات تنفيذ متعددة (بطيء، عادي، سريع، فوري)
- إبر<PERSON>ز العناصر أثناء التنفيذ
- تقارير مفصلة عن النتائج

### 🎨 واجهة مستخدم احترافية
- تصميم Material Design حديث
- نظام تبويبات متقدم
- وضع داكن/فاتح
- واجهة عربية كاملة

### 📁 إدارة المشاريع
- تنظيم الإجراءات في مشاريع
- تصدير واستيراد المشاريع
- نظام علامات (Tags) للتصنيف
- نسخ احتياطي تلقائي

### 📋 قوالب جاهزة
- قوالب للمهام الشائعة
- قوالب تسجيل الدخول
- قوالب النماذج
- قوالب التجارة الإلكترونية

## 🚀 أنواع الإجراءات المدعومة

### الإجراءات الأساسية
- **النقر** - نقر على العناصر
- **الكتابة** - إدخال النصوص
- **الاختيار** - اختيار من القوائم المنسدلة
- **التمرير** - تمرير الصفحة
- **الانتظار** - توقف مؤقت

### الإجراءات المتقدمة
- **تمرير الماوس** - Hover على العناصر
- **استخراج البيانات** - جمع المعلومات
- **الشروط** - تنفيذ مشروط
- **الحلقات** - تكرار الإجراءات
- **المتغيرات** - تخزين واستخدام البيانات
- **JavaScript** - تنفيذ كود مخصص

## 📦 التثبيت

### من Chrome Web Store
1. اذهب إلى Chrome Web Store
2. ابحث عن "WebFlow Automator Pro"
3. انقر على "إضافة إلى Chrome"

### التثبيت اليدوي
1. حمل الملفات من GitHub
2. افتح Chrome واذهب إلى `chrome://extensions/`
3. فعل "وضع المطور"
4. انقر على "تحميل إضافة غير مضغوطة"
5. اختر مجلد المشروع

## 🛠️ الاستخدام

### البدء السريع
1. انقر على أيقونة الإضافة
2. اذهب إلى تبويب "التسجيل"
3. انقر على "بدء التسجيل"
4. قم بالإجراءات المطلوبة على الموقع
5. انقر على "إيقاف التسجيل"
6. اذهب إلى تبويب "الإجراءات" وانقر على "تنفيذ"

### إضافة إجراء يدوي
1. اذهب إلى تبويب "التسجيل"
2. اختر نوع الإجراء
3. انقر على أيقونة الهدف لتحديد العنصر
4. أدخل القيمة المطلوبة
5. انقر على "إضافة الإجراء"

### إدارة المشاريع
1. اذهب إلى تبويب "المشاريع"
2. انقر على "مشروع جديد"
3. أدخل اسم ووصف المشروع
4. احفظ الإجراءات في المشروع
5. صدر أو استورد المشاريع حسب الحاجة

## 🏗️ البنية التقنية

### الملفات الرئيسية
```
├── manifest.json          # إعدادات الإضافة
├── popup.html             # واجهة المستخدم الرئيسية
├── content.js             # سكريبت المحتوى
├── js/
│   ├── background.js      # سكريبت الخلفية
│   ├── utils/
│   │   └── helpers.js     # وظائف مساعدة
│   ├── core/
│   │   ├── element-selector.js    # محدد العناصر
│   │   └── action-executor.js     # منفذ الإجراءات
│   └── popup/
│       ├── ui-manager.js          # مدير الواجهة
│       ├── project-manager.js     # مدير المشاريع
│       └── template-manager.js    # مدير القوالب
├── css/
│   ├── popup.css          # أنماط الواجهة
│   ├── content.css        # أنماط المحتوى
│   └── icons.css          # أيقونات
└── icons/                 # أيقونات الإضافة
```

### التقنيات المستخدمة
- **Manifest V3** - أحدث إصدار من Chrome Extensions
- **Vanilla JavaScript** - بدون مكتبات خارجية
- **CSS Grid & Flexbox** - تخطيط متجاوب
- **Chrome Storage API** - تخزين البيانات
- **Chrome Tabs API** - التفاعل مع التبويبات

## 🔧 التطوير

### متطلبات التطوير
- Chrome Browser
- محرر نصوص (VS Code مُوصى به)
- معرفة بـ JavaScript و CSS

### إعداد بيئة التطوير
1. استنسخ المستودع
```bash
git clone https://github.com/your-username/webflow-automator-pro.git
cd webflow-automator-pro
```

2. افتح Chrome Extensions
```
chrome://extensions/
```

3. فعل وضع المطور وحمل الإضافة

### هيكل الكود
- **Modular Architecture** - كود منظم في وحدات
- **Event-Driven** - نظام أحداث متقدم
- **Error Handling** - معالجة شاملة للأخطاء
- **Performance Optimized** - محسن للأداء

## 🧪 الاختبار

### اختبار الوظائف
1. اختبر التسجيل على مواقع مختلفة
2. تأكد من دقة محدد العناصر
3. اختبر التنفيذ بسرعات مختلفة
4. تحقق من معالجة الأخطاء

### اختبار الأداء
- قياس سرعة التنفيذ
- مراقبة استهلاك الذاكرة
- اختبار على صفحات كبيرة

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع هذه الخطوات:

1. Fork المستودع
2. أنشئ فرع للميزة الجديدة
3. اكتب الكود مع التعليقات
4. اختبر التغييرات
5. أرسل Pull Request

### إرشادات الكود
- استخدم أسماء متغيرات واضحة
- اكتب تعليقات باللغة العربية
- اتبع نمط الكود الموجود
- اختبر جميع التغييرات

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم

### الحصول على المساعدة
- [الوثائق الكاملة](https://webflow-automator.com/docs)
- [الأسئلة الشائعة](https://webflow-automator.com/faq)
- [تقرير مشكلة](https://github.com/your-username/webflow-automator-pro/issues)

### التواصل
- البريد الإلكتروني: <EMAIL>
- تويتر: [@WebFlowAutomator](https://twitter.com/WebFlowAutomator)

## 🔄 سجل التغييرات

### الإصدار 2.0.0
- إعادة تصميم كاملة للواجهة
- محرك تنفيذ جديد ومحسن
- نظام مشاريع متقدم
- قوالب جاهزة
- دعم المتغيرات والشروط

### الإصدار 1.0.0
- الإصدار الأولي
- تسجيل وتنفيذ أساسي
- واجهة بسيطة

## 🚀 الخطط المستقبلية

- [ ] دعم المزيد من أنواع الإجراءات
- [ ] تكامل مع APIs خارجية
- [ ] تصدير إلى تنسيقات مختلفة
- [ ] تطبيق ويب مصاحب
- [ ] دعم المزيد من المتصفحات

---

**WebFlow Automator Pro** - أتمتة احترافية لمواقع الويب 🚀
