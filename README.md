# 🤖 أداة الأتمتة الذكية

إضافة Chrome شخصية لأتمتة المواقع - بسيطة وقوية وسهلة الاستخدام!

## ✨ الميزات الرئيسية

### 📹 تسجيل تلقائي للإجراءات
- تسجيل النقرات والكتابة تلقائياً
- محدد عناصر ذكي ومرئي
- واجهة بسيطة وسهلة الاستخدام

### ⚡ تنفيذ سريع وموثوق
- تنفيذ الإجراءات بسرعات مختلفة
- معالجة أخطاء ذكية
- إشعارات واضحة للنتائج

### 🎯 أنواع إجراءات متنوعة
- **👆 النقر** - النقر على الأزرار والروابط
- **⌨️ الكتابة** - إدخال النصوص في الحقول
- **⏰ الانتظار** - توقف مؤقت بين الإجراءات
- **📜 التمرير** - تمرير الصفحة في جميع الاتجاهات

## 🚀 التثبيت السريع

### الخطوة 1: تحضير الملفات
تأكد من وجود هذه الملفات في مجلد واحد:
```
📁 أداة-الأتمتة-الذكية/
├── 📄 manifest.json
├── 🎨 popup.html
├── 🎨 popup.css
├── 📜 popup.js
├── 📜 content.js
├── 📜 content.css
├── 📜 background.js
└── 📁 icons/
    ├── icon16.svg
    ├── icon32.svg
    ├── icon48.svg
    └── icon128.svg
```

### الخطوة 2: تثبيت في Chrome
1. افتح Chrome واذهب إلى: `chrome://extensions/`
2. فعل "وضع المطور" (Developer mode)
3. انقر على "تحميل إضافة غير مضغوطة" (Load unpacked)
4. اختر مجلد الإضافة
5. ستظهر الأيقونة في شريط الأدوات! 🎉

## 🎯 كيفية الاستخدام

### تسجيل الإجراءات
1. انقر على أيقونة الإضافة 🤖
2. انقر على "🔴 بدء التسجيل"
3. قم بالإجراءات المطلوبة على الموقع
4. انقر على "⏹️ إيقاف التسجيل"

### إضافة إجراء يدوي
1. اختر نوع الإجراء من القائمة
2. انقر على 🎯 لتحديد العنصر
3. أدخل القيمة المطلوبة
4. انقر على "➕ إضافة الإجراء"

### تنفيذ الإجراءات
1. اختر سرعة التنفيذ (بطيء/عادي/سريع)
2. انقر على "▶️ تنفيذ الإجراءات"
3. شاهد الإجراءات تتنفذ تلقائياً! ✨

## 🛠️ الميزات التقنية

### تقنيات حديثة
- **Manifest V3** - أحدث معايير Chrome
- **JavaScript ES6+** - كود حديث ومحسن
- **CSS Grid & Flexbox** - تصميم متجاوب
- **SVG Icons** - أيقونات عالية الجودة

### أمان وخصوصية
- 🔒 جميع البيانات محلية في متصفحك
- 🚫 لا نرسل أي معلومات لخوادر خارجية
- ✅ كود مفتوح المصدر وقابل للمراجعة

## 🧪 اختبار الإضافة

جرب الإضافة على:
- نماذج تسجيل الدخول
- نماذج الاتصال
- مواقع التسوق
- أي موقع تريد أتمتته!

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة وحلولها

**❌ الإضافة لا تظهر**
- ✅ تأكد من تفعيل "وضع المطور"
- ✅ تحقق من وجود ملف manifest.json

**❌ لا يمكن تحديد العناصر**
- ✅ تأكد من تحميل الصفحة بالكامل
- ✅ جرب إعادة تحميل الصفحة

**❌ الإجراءات لا تعمل**
- ✅ زد وقت الانتظار بين الإجراءات
- ✅ تحقق من صحة محددات العناصر

## 💡 نصائح للاستخدام الأمثل

1. **🐌 ابدأ بسرعة بطيئة** للمواقع المعقدة
2. **⏰ استخدم أوقات انتظار مناسبة** للمواقع البطيئة
3. **🧪 اختبر الإجراءات** قبل التنفيذ النهائي
4. **📝 ابدأ بإجراءات بسيطة** ثم تدرج للمعقد

## 🎨 تخصيص الإضافة

يمكنك تخصيص:
- ألوان الواجهة في `popup.css`
- نصوص الواجهة في `popup.html`
- سلوك الإضافة في `popup.js`

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تحقق من وحدة التحكم (F12)
2. ابحث عن رسائل الخطأ
3. جرب إعادة تحميل الإضافة

## 🚀 التطوير المستقبلي

خطط للتحسين:
- [ ] دعم المزيد من أنواع الإجراءات
- [ ] حفظ وتحميل سيناريوهات
- [ ] واجهة أكثر تقدماً
- [ ] دعم المتغيرات والشروط

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - يمكنك استخدامه وتعديله بحرية!

---

## 🎉 استمتع بأتمتة مهامك!

**أداة الأتمتة الذكية** - اجعل حياتك أسهل مع الأتمتة الذكية! 🤖✨

### 📸 لقطات شاشة

```
🖥️ الواجهة الرئيسية
┌─────────────────────────────┐
│ 🤖 أداة الأتمتة الذكية      │
├─────────────────────────────┤
│ 📹 تسجيل الإجراءات         │
│ [🔴 بدء التسجيل]           │
│ [⏹️ إيقاف التسجيل]         │
├─────────────────────────────┤
│ ➕ إضافة إجراء يدوي        │
│ نوع الإجراء: [👆 نقر ▼]    │
│ العنصر: [______] [🎯]      │
│ [➕ إضافة الإجراء]          │
├─────────────────────────────┤
│ 📋 قائمة الإجراءات         │
│ السرعة: [🚶 عادي ▼]        │
│ [▶️ تنفيذ الإجراءات]       │
└─────────────────────────────┘
```

**🎯 جاهز للاستخدام!** قم بتثبيت الإضافة وابدأ رحلتك في عالم الأتمتة! 🚀
