# دليل التثبيت والاستخدام - WebFlow Automator Pro

## 📋 متطلبات النظام

### المتطلبات الأساسية
- **Google Chrome** الإصدار 88 أو أحدث
- **نظام التشغيل**: Windows 10+، macOS 10.14+، أو Linux
- **الذاكرة**: 4 جيجابايت RAM على الأقل
- **مساحة القرص**: 50 ميجابايت مساحة فارغة

### المتطلبات الاختيارية
- **Python 3.7+** (لإنشاء الأيقونات المخصصة)
- **Node.js 14+** (للتطوير)

## 🚀 طرق التثبيت

### الطريقة 1: التثبيت من Chrome Web Store (قريباً)
1. افتح متجر Chrome Web Store
2. ابحث عن "WebFlow Automator Pro"
3. انقر على "إضافة إلى Chrome"
4. اقبل الصلاحيات المطلوبة
5. ستظهر أيقونة الإضافة في شريط الأدوات

### الطريقة 2: التثبيت اليدوي (للتطوير)

#### الخطوة 1: تحميل الملفات
```bash
# استنساخ المستودع
git clone https://github.com/webflow-team/webflow-automator-pro.git

# أو تحميل ZIP
# حمل الملف المضغوط من GitHub وفك الضغط
```

#### الخطوة 2: إنشاء الأيقونات (اختياري)
```bash
# انتقل إلى مجلد المشروع
cd webflow-automator-pro

# إنشاء الأيقونات (يتطلب Python و Pillow)
pip install Pillow
python icons/create_icons.py
```

#### الخطوة 3: تحميل الإضافة في Chrome
1. افتح Google Chrome
2. اذهب إلى `chrome://extensions/`
3. فعل "وضع المطور" (Developer mode) في الزاوية العلوية اليمنى
4. انقر على "تحميل إضافة غير مضغوطة" (Load unpacked)
5. اختر مجلد `webflow-automator-pro`
6. انقر على "اختيار مجلد"

#### الخطوة 4: التحقق من التثبيت
- ستظهر أيقونة WebFlow في شريط الأدوات
- انقر على الأيقونة للتأكد من فتح الواجهة
- تحقق من ظهور رسالة "جاهز" في أسفل النافذة

## 🎯 الاستخدام الأساسي

### البدء السريع - تسجيل أول إجراء

#### 1. فتح الإضافة
- انقر على أيقونة WebFlow في شريط الأدوات
- أو استخدم الاختصار `Ctrl+Shift+W` (Windows) أو `Cmd+Shift+W` (Mac)

#### 2. بدء التسجيل
1. اذهب إلى الموقع المراد أتمتته
2. في نافذة الإضافة، انقر على تبويب "التسجيل"
3. انقر على زر "بدء التسجيل"
4. ستظهر رسالة تأكيد وسيبدأ التسجيل

#### 3. تنفيذ الإجراءات
1. قم بالإجراءات المطلوبة على الموقع:
   - النقر على الأزرار
   - ملء النماذج
   - اختيار من القوائم
   - التمرير في الصفحة
2. ستسجل الإضافة كل إجراء تلقائياً

#### 4. إيقاف التسجيل
1. انقر على زر "إيقاف التسجيل"
2. ستظهر قائمة بالإجراءات المسجلة
3. يمكنك مراجعة وتعديل الإجراءات

#### 5. تشغيل الإجراءات
1. اذهب إلى تبويب "الإجراءات"
2. راجع قائمة الإجراءات
3. اختر إعدادات التنفيذ (السرعة، معالجة الأخطاء)
4. انقر على "تنفيذ الإجراءات"

## 🛠️ الاستخدام المتقدم

### إضافة إجراءات يدوية

#### 1. فتح نموذج الإجراء
- اذهب إلى تبويب "التسجيل"
- انتقل إلى قسم "إضافة إجراء يدوي"

#### 2. تحديد نوع الإجراء
اختر من الأنواع المتاحة:
- **نقر**: للنقر على العناصر
- **كتابة نص**: لإدخال النصوص
- **اختيار من قائمة**: للقوائم المنسدلة
- **تمرير الماوس**: لتفعيل القوائم المخفية
- **تمرير الصفحة**: للتنقل في الصفحة
- **انتظار**: للتوقف المؤقت
- **استخراج بيانات**: لجمع المعلومات
- **شرط**: للتنفيذ المشروط
- **حلقة تكرار**: لتكرار الإجراءات
- **متغير**: لتخزين البيانات
- **كود JavaScript**: لتنفيذ كود مخصص

#### 3. تحديد العنصر
- انقر على أيقونة الهدف 🎯
- ستنتقل إلى وضع تحديد العنصر
- انقر على العنصر المطلوب في الصفحة
- سيتم ملء محدد العنصر تلقائياً

#### 4. إدخال القيمة
- أدخل النص أو القيمة المطلوبة
- اضبط أوقات الانتظار حسب الحاجة

#### 5. إضافة الإجراء
- انقر على "إضافة الإجراء"
- أو "اختبار الإجراء" للتجربة أولاً

### إدارة المشاريع

#### إنشاء مشروع جديد
1. اذهب إلى تبويب "المشاريع"
2. انقر على "مشروع جديد"
3. أدخل اسم ووصف المشروع
4. احفظ المشروع

#### حفظ الإجراءات في مشروع
1. سجل أو أضف الإجراءات المطلوبة
2. اذهب إلى تبويب "المشاريع"
3. اختر المشروع المطلوب
4. انقر على "حفظ الإجراءات الحالية"

#### تصدير واستيراد المشاريع
**التصدير:**
1. اختر المشروع المطلوب
2. انقر على أيقونة التصدير 📤
3. احفظ الملف في المكان المطلوب

**الاستيراد:**
1. انقر على "استيراد"
2. اختر ملف المشروع (.json)
3. سيتم إضافة المشروع إلى القائمة

### استخدام القوالب

#### تطبيق قالب جاهز
1. اذهب إلى تبويب "القوالب"
2. تصفح القوالب المتاحة
3. انقر على "استخدام" للقالب المطلوب
4. أدخل المتغيرات المطلوبة (إن وجدت)
5. ستضاف الإجراءات إلى قائمتك

#### القوالب المتاحة
- **تسجيل الدخول**: لمواقع تسجيل الدخول
- **نموذج اتصال**: لنماذج التواصل
- **البحث والتصفية**: للبحث في المواقع
- **منشور وسائل التواصل**: للنشر على المنصات
- **استخراج البيانات**: لجمع المعلومات
- **إتمام الشراء**: للمتاجر الإلكترونية

## ⚙️ الإعدادات والتخصيص

### إعدادات التنفيذ
- **السرعة**: بطيء، عادي، سريع، فوري
- **معالجة الأخطاء**: متابعة أو إيقاف عند الخطأ
- **إبراز العناصر**: تفعيل/إلغاء إبراز العناصر

### إعدادات التسجيل
- **جودة التسجيل**: عادي أو عالي
- **تسجيل التمرير**: تفعيل/إلغاء
- **تسجيل الحركة**: تفعيل/إلغاء

## 🔧 استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة

#### الإضافة لا تعمل
**الأعراض**: لا تفتح نافذة الإضافة
**الحلول**:
1. تأكد من تفعيل الإضافة في `chrome://extensions/`
2. أعد تحميل الصفحة
3. أعد تشغيل Chrome

#### لا يمكن تحديد العناصر
**الأعراض**: لا يعمل محدد العناصر
**الحلول**:
1. تأكد من تحميل الصفحة بالكامل
2. تحقق من عدم وجود إطارات (iframes) معقدة
3. جرب استخدام محدد CSS يدوياً

#### فشل في تنفيذ الإجراءات
**الأعراض**: توقف التنفيذ أو أخطاء
**الحلول**:
1. تحقق من صحة محددات العناصر
2. زد أوقات الانتظار
3. استخدم وضع "إيقاف عند الخطأ" للتشخيص

#### بطء في الأداء
**الأعراض**: تنفيذ بطيء أو تجمد
**الحلول**:
1. قلل عدد الإجراءات المتزامنة
2. زد أوقات الانتظار
3. أغلق التبويبات غير المستخدمة

### رسائل الخطأ الشائعة

#### "لا يمكن الاتصال بالصفحة"
- تأكد من تحميل الصفحة
- أعد تحميل الصفحة
- تحقق من صلاحيات الإضافة

#### "العنصر غير موجود"
- تحقق من محدد العنصر
- تأكد من ظهور العنصر في الصفحة
- جرب محدد مختلف

#### "انتهت مهلة الانتظار"
- زد وقت الانتظار
- تحقق من سرعة الإنترنت
- تأكد من استجابة الموقع

## 📞 الحصول على المساعدة

### الموارد المتاحة
- **الوثائق الكاملة**: [webflow-automator.com/docs](https://webflow-automator.com/docs)
- **الأسئلة الشائعة**: [webflow-automator.com/faq](https://webflow-automator.com/faq)
- **فيديوهات تعليمية**: [youtube.com/webflowautomator](https://youtube.com/webflowautomator)

### التواصل للدعم
- **البريد الإلكتروني**: <EMAIL>
- **تقرير مشكلة**: [GitHub Issues](https://github.com/webflow-team/webflow-automator-pro/issues)
- **المجتمع**: [Discord Server](https://discord.gg/webflowautomator)

### قبل طلب المساعدة
1. تحقق من الأسئلة الشائعة
2. جرب الحلول المقترحة أعلاه
3. اجمع معلومات عن المشكلة:
   - إصدار Chrome
   - إصدار الإضافة
   - رسالة الخطأ (إن وجدت)
   - خطوات إعادة إنتاج المشكلة

---

**نصيحة**: ابدأ بمشاريع بسيطة وتدرج إلى المعقدة لتتعلم الإضافة بشكل أفضل! 🚀
