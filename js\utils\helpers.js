/**
 * WebFlow Automator Pro - Utility Helper Functions
 * Common utility functions used across the extension
 */

// DOM Utilities
const DOMUtils = {
  /**
   * Wait for element to be available in DOM
   * @param {string} selector - CSS selector
   * @param {number} timeout - Timeout in milliseconds
   * @returns {Promise<Element>}
   */
  waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  },

  /**
   * Check if element is visible
   * @param {Element} element
   * @returns {boolean}
   */
  isElementVisible(element) {
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);
    
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.visibility !== 'hidden' &&
      style.display !== 'none' &&
      style.opacity !== '0'
    );
  },

  /**
   * Scroll element into view smoothly
   * @param {Element} element
   */
  scrollIntoView(element) {
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'center'
      });
    }
  },

  /**
   * Highlight element with visual indicator
   * @param {Element} element
   * @param {string} color
   */
  highlightElement(element, color = '#ff6b6b') {
    if (!element) return;
    
    const originalStyle = {
      outline: element.style.outline,
      outlineOffset: element.style.outlineOffset
    };
    
    element.style.outline = `3px solid ${color}`;
    element.style.outlineOffset = '2px';
    
    setTimeout(() => {
      element.style.outline = originalStyle.outline;
      element.style.outlineOffset = originalStyle.outlineOffset;
    }, 2000);
  }
};

// String Utilities
const StringUtils = {
  /**
   * Generate unique ID
   * @returns {string}
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  /**
   * Sanitize string for use as filename
   * @param {string} str
   * @returns {string}
   */
  sanitizeFilename(str) {
    return str.replace(/[^a-z0-9\u0600-\u06FF]/gi, '_').toLowerCase();
  },

  /**
   * Truncate string with ellipsis
   * @param {string} str
   * @param {number} maxLength
   * @returns {string}
   */
  truncate(str, maxLength = 50) {
    if (str.length <= maxLength) return str;
    return str.substr(0, maxLength - 3) + '...';
  },

  /**
   * Format date to Arabic locale
   * @param {Date} date
   * @returns {string}
   */
  formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  }
};

// Validation Utilities
const ValidationUtils = {
  /**
   * Validate CSS selector
   * @param {string} selector
   * @returns {boolean}
   */
  isValidSelector(selector) {
    try {
      document.querySelector(selector);
      return true;
    } catch (e) {
      return false;
    }
  },

  /**
   * Validate URL
   * @param {string} url
   * @returns {boolean}
   */
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  },

  /**
   * Validate email
   * @param {string} email
   * @returns {boolean}
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
};

// Storage Utilities
const StorageUtils = {
  /**
   * Get data from chrome storage
   * @param {string|string[]} keys
   * @returns {Promise<any>}
   */
  async get(keys) {
    return new Promise((resolve) => {
      chrome.storage.local.get(keys, resolve);
    });
  },

  /**
   * Set data to chrome storage
   * @param {object} data
   * @returns {Promise<void>}
   */
  async set(data) {
    return new Promise((resolve) => {
      chrome.storage.local.set(data, resolve);
    });
  },

  /**
   * Remove data from chrome storage
   * @param {string|string[]} keys
   * @returns {Promise<void>}
   */
  async remove(keys) {
    return new Promise((resolve) => {
      chrome.storage.local.remove(keys, resolve);
    });
  },

  /**
   * Clear all storage data
   * @returns {Promise<void>}
   */
  async clear() {
    return new Promise((resolve) => {
      chrome.storage.local.clear(resolve);
    });
  }
};

// Event Utilities
const EventUtils = {
  /**
   * Debounce function execution
   * @param {Function} func
   * @param {number} wait
   * @returns {Function}
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * Throttle function execution
   * @param {Function} func
   * @param {number} limit
   * @returns {Function}
   */
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
};

// Error Handling Utilities
const ErrorUtils = {
  /**
   * Log error with context
   * @param {Error} error
   * @param {string} context
   */
  logError(error, context = '') {
    console.error(`[WebFlow Automator] ${context}:`, error);
    
    // Send error to background script for logging
    if (chrome.runtime) {
      chrome.runtime.sendMessage({
        action: 'logError',
        error: {
          message: error.message,
          stack: error.stack,
          context: context,
          timestamp: new Date().toISOString()
        }
      }).catch(() => {
        // Ignore if background script is not available
      });
    }
  },

  /**
   * Create user-friendly error message
   * @param {Error} error
   * @returns {string}
   */
  getUserFriendlyMessage(error) {
    const errorMessages = {
      'Element not found': 'لم يتم العثور على العنصر المطلوب',
      'Timeout': 'انتهت مهلة الانتظار',
      'Permission denied': 'تم رفض الإذن',
      'Network error': 'خطأ في الشبكة',
      'Invalid selector': 'محدد العنصر غير صحيح'
    };

    for (const [key, value] of Object.entries(errorMessages)) {
      if (error.message.includes(key)) {
        return value;
      }
    }

    return 'حدث خطأ غير متوقع';
  }
};

// Performance Utilities
const PerformanceUtils = {
  /**
   * Measure function execution time
   * @param {Function} func
   * @param {string} label
   * @returns {any}
   */
  async measureTime(func, label = 'Function') {
    const start = performance.now();
    const result = await func();
    const end = performance.now();
    console.log(`[Performance] ${label}: ${(end - start).toFixed(2)}ms`);
    return result;
  },

  /**
   * Create performance observer
   * @param {string} entryType
   * @param {Function} callback
   */
  observePerformance(entryType, callback) {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver(callback);
      observer.observe({ entryTypes: [entryType] });
      return observer;
    }
  }
};

// Export utilities for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    DOMUtils,
    StringUtils,
    ValidationUtils,
    StorageUtils,
    EventUtils,
    ErrorUtils,
    PerformanceUtils
  };
}
