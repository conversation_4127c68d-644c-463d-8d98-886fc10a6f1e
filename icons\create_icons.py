#!/usr/bin/env python3
"""
WebFlow Automator Pro - Icon Generator
Creates PNG icons for the Chrome extension
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, output_path):
    """Create a professional icon for WebFlow Automator Pro"""
    
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Define colors
    primary_color = (102, 126, 234)  # #667eea
    secondary_color = (118, 75, 162)  # #764ba2
    white = (255, 255, 255)
    
    # Create gradient background circle
    center = size // 2
    radius = size // 2 - 2
    
    # Draw gradient circle (simplified)
    for i in range(radius):
        alpha = int(255 * (1 - i / radius))
        color = (*primary_color, alpha)
        draw.ellipse([center - radius + i, center - radius + i, 
                     center + radius - i, center + radius - i], 
                    fill=color)
    
    # Draw main circle
    draw.ellipse([2, 2, size-2, size-2], fill=primary_color)
    
    # Draw automation symbol (gear-like)
    gear_size = size // 3
    gear_center = center
    
    # Draw gear teeth
    for angle in range(0, 360, 45):
        import math
        x1 = gear_center + (gear_size // 2) * math.cos(math.radians(angle))
        y1 = gear_center + (gear_size // 2) * math.sin(math.radians(angle))
        x2 = gear_center + (gear_size // 2 + 4) * math.cos(math.radians(angle))
        y2 = gear_center + (gear_size // 2 + 4) * math.sin(math.radians(angle))
        draw.line([x1, y1, x2, y2], fill=white, width=2)
    
    # Draw center circle
    inner_radius = gear_size // 4
    draw.ellipse([center - inner_radius, center - inner_radius,
                 center + inner_radius, center + inner_radius], 
                fill=white)
    
    # Draw flow arrows
    arrow_size = size // 8
    # Right arrow
    draw.polygon([
        (center + gear_size//2 + 8, center - arrow_size//2),
        (center + gear_size//2 + 8 + arrow_size, center),
        (center + gear_size//2 + 8, center + arrow_size//2)
    ], fill=white)
    
    # Save the image
    img.save(output_path, 'PNG')
    print(f"Created icon: {output_path}")

def main():
    """Generate all required icon sizes"""
    
    # Create icons directory if it doesn't exist
    os.makedirs('icons', exist_ok=True)
    
    # Icon sizes required for Chrome extensions
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        output_path = f'icons/icon{size}.png'
        create_icon(size, output_path)
    
    print("All icons created successfully!")
    print("\nTo use these icons:")
    print("1. Install Pillow: pip install Pillow")
    print("2. Run this script: python create_icons.py")
    print("3. The icons will be created in the icons/ directory")

if __name__ == "__main__":
    main()
