{"name": "webflow-automator-pro", "version": "2.0.0", "description": "إضافة Chrome احترافية لأتمتة المواقع مع ميزات متقدمة للتسجيل والتشغيل التلقائي", "main": "popup.js", "scripts": {"build": "npm run build:icons && npm run build:zip", "build:icons": "python icons/create_icons.py", "build:zip": "zip -r webflow-automator-pro.zip . -x '*.git*' 'node_modules/*' '*.zip' 'package*.json' 'README.md' '.vscode/*' '*.py'", "dev": "echo 'Load extension in Chrome from this directory'", "test": "echo 'No tests specified yet'", "lint": "echo '<PERSON><PERSON> not configured yet'", "format": "echo 'Formatting not configured yet'"}, "keywords": ["chrome-extension", "automation", "web-automation", "browser-automation", "workflow", "productivity", "arabic", "webflow"], "author": {"name": "WebFlow Team", "email": "<EMAIL>", "url": "https://webflow-automator.com"}, "license": "MIT", "homepage": "https://webflow-automator.com", "repository": {"type": "git", "url": "https://github.com/webflow-team/webflow-automator-pro.git"}, "bugs": {"url": "https://github.com/webflow-team/webflow-automator-pro/issues", "email": "<EMAIL>"}, "engines": {"node": ">=14.0.0"}, "devDependencies": {"web-ext": "^7.0.0"}, "webExt": {"sourceDir": ".", "artifactsDir": "dist", "ignoreFiles": ["package.json", "package-lock.json", "node_modules", ".git", ".vscode", "*.md", "*.py"]}, "manifest": {"version": "2.0.0", "name": "WebFlow Automator Pro", "description": "إضافة احترافية لأتمتة المواقع مع ميزات متقدمة للتسجيل والتشغيل التلقائي"}, "chrome": {"minimum_version": "88"}, "permissions": ["activeTab", "storage", "scripting", "tabs", "background", "notifications", "downloads"], "features": ["تسجيل الإجراءات التلقائي", "م<PERSON><PERSON><PERSON> عناصر ذكي", "تنفيذ متقدم مع معالجة الأخطاء", "واجهة مستخدم احترافية", "إدارة المشاريع", "قوالب جاهزة", "دعم المتغيرات والشروط", "تصدير واستيراد", "تقارير مفصلة"], "supported_actions": ["click", "type", "select", "hover", "scroll", "wait", "extract", "condition", "loop", "variable", "javascript"], "supported_selectors": ["CSS Selectors", "XPath", "ID", "Class", "Attribute", "Text Content"], "languages": ["ar", "en"], "categories": ["productivity", "automation", "developer-tools"]}