/**
 * أداة الأتمتة الذكية - أنماط المحتوى
 * أنماط للعناصر التي تظهر على الصفحات
 */

/* Overlay للتحديد */
.automation-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(0, 0, 0, 0.3) !important;
  z-index: 999999 !important;
  cursor: crosshair !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* مربع التمييز */
.automation-highlight {
  position: absolute !important;
  border: 3px solid #667eea !important;
  background: rgba(102, 126, 234, 0.1) !important;
  pointer-events: none !important;
  z-index: 1000000 !important;
  border-radius: 4px !important;
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.5) !important;
  transition: all 0.1s ease !important;
}

/* نص التعليمات */
.automation-instructions {
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: #333 !important;
  color: white !important;
  padding: 15px 25px !important;
  border-radius: 8px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  z-index: 1000001 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  text-align: center !important;
  direction: rtl !important;
  animation: automation-fade-in 0.3s ease !important;
}

/* مؤشر التسجيل */
.automation-recording-indicator {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: #ff4757 !important;
  color: white !important;
  padding: 10px 15px !important;
  border-radius: 20px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  z-index: 1000001 !important;
  box-shadow: 0 2px 10px rgba(255, 71, 87, 0.3) !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  animation: automation-pulse 2s infinite !important;
}

.automation-recording-indicator::before {
  content: '🔴' !important;
  animation: automation-blink 1s infinite !important;
}

/* إشعارات التنفيذ */
.automation-notification {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  padding: 15px 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 1000001 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  max-width: 350px !important;
  animation: automation-slide-in 0.3s ease !important;
  direction: rtl !important;
}

.automation-notification.success {
  border-left: 4px solid #2ed573 !important;
}

.automation-notification.error {
  border-left: 4px solid #ff4757 !important;
}

.automation-notification.warning {
  border-left: 4px solid #ffa502 !important;
}

.automation-notification.info {
  border-left: 4px solid #3742fa !important;
}

.automation-notification .title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #333 !important;
  margin-bottom: 5px !important;
}

.automation-notification .message {
  font-size: 12px !important;
  color: #666 !important;
  line-height: 1.4 !important;
}

/* تمييز العناصر أثناء التنفيذ */
.automation-executing {
  outline: 3px solid #667eea !important;
  outline-offset: 2px !important;
  background: rgba(102, 126, 234, 0.1) !important;
  animation: automation-glow 1s ease-in-out !important;
}

/* أنيميشن للعناصر المنفذة */
.automation-executed {
  animation: automation-success-flash 0.5s ease !important;
}

/* الأنيميشن */
@keyframes automation-fade-in {
  from {
    opacity: 0 !important;
    transform: translateX(-50%) translateY(-10px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateX(-50%) translateY(0) !important;
  }
}

@keyframes automation-slide-in {
  from {
    opacity: 0 !important;
    transform: translateX(20px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateX(0) !important;
  }
}

@keyframes automation-pulse {
  0%, 100% {
    transform: scale(1) !important;
  }
  50% {
    transform: scale(1.05) !important;
  }
}

@keyframes automation-blink {
  0%, 50% {
    opacity: 1 !important;
  }
  51%, 100% {
    opacity: 0.3 !important;
  }
}

@keyframes automation-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.5) !important;
  }
  50% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.8) !important;
  }
}

@keyframes automation-success-flash {
  0% {
    background: rgba(46, 213, 115, 0.3) !important;
  }
  100% {
    background: transparent !important;
  }
}

/* الوضع الداكن */
@media (prefers-color-scheme: dark) {
  .automation-instructions {
    background: #2c2c2c !important;
    color: #f0f0f0 !important;
  }
  
  .automation-notification {
    background: #2c2c2c !important;
    border-color: #404040 !important;
  }
  
  .automation-notification .title {
    color: #f0f0f0 !important;
  }
  
  .automation-notification .message {
    color: #ccc !important;
  }
}

/* تحسينات الوصولية */
@media (prefers-reduced-motion: reduce) {
  .automation-highlight,
  .automation-instructions,
  .automation-notification {
    animation: none !important;
    transition: none !important;
  }
  
  .automation-recording-indicator {
    animation: none !important;
  }
  
  .automation-recording-indicator::before {
    animation: none !important;
  }
  
  .automation-executing {
    animation: none !important;
  }
  
  .automation-executed {
    animation: none !important;
  }
}

/* إخفاء العناصر عند الطباعة */
@media print {
  .automation-overlay,
  .automation-highlight,
  .automation-instructions,
  .automation-recording-indicator,
  .automation-notification {
    display: none !important;
  }
}
