<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg128" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  <circle cx="64" cy="64" r="60" fill="url(#bg128)" filter="url(#shadow)"/>
  <circle cx="64" cy="64" r="30" fill="none" stroke="white" stroke-width="3"/>
  
  <!-- Gear teeth -->
  <rect x="63" y="10" width="2" height="8" fill="white"/>
  <rect x="63" y="110" width="2" height="8" fill="white"/>
  <rect x="10" y="63" width="8" height="2" fill="white"/>
  <rect x="110" y="63" width="8" height="2" fill="white"/>
  
  <rect x="90" y="26" width="2" height="8" fill="white" transform="rotate(45 91 30)"/>
  <rect x="26" y="94" width="2" height="8" fill="white" transform="rotate(45 27 98)"/>
  <rect x="26" y="26" width="2" height="8" fill="white" transform="rotate(-45 27 30)"/>
  <rect x="94" y="94" width="2" height="8" fill="white" transform="rotate(-45 95 98)"/>
  
  <!-- Inner circle -->
  <circle cx="64" cy="64" r="15" fill="white"/>
  <circle cx="64" cy="64" r="8" fill="url(#bg128)"/>
  
  <!-- Flow arrows -->
  <polygon points="85,58 95,64 85,70" fill="white" opacity="0.9"/>
  <polygon points="43,58 33,64 43,70" fill="white" opacity="0.9"/>
  <polygon points="58,43 64,33 70,43" fill="white" opacity="0.9"/>
  <polygon points="58,85 64,95 70,85" fill="white" opacity="0.9"/>
  
  <!-- Center dot -->
  <circle cx="64" cy="64" r="3" fill="white"/>
</svg>
