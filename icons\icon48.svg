<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg48" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
  </defs>
  <circle cx="24" cy="24" r="22" fill="url(#bg48)"/>
  <circle cx="24" cy="24" r="12" fill="none" stroke="white" stroke-width="2"/>
  <rect x="23" y="6" width="2" height="4" fill="white"/>
  <rect x="23" y="38" width="2" height="4" fill="white"/>
  <rect x="6" y="23" width="4" height="2" fill="white"/>
  <rect x="38" y="23" width="4" height="2" fill="white"/>
  <rect x="35" y="11" width="2" height="4" fill="white" transform="rotate(45 36 13)"/>
  <rect x="11" y="33" width="2" height="4" fill="white" transform="rotate(45 12 35)"/>
  <rect x="11" y="11" width="2" height="4" fill="white" transform="rotate(-45 12 13)"/>
  <rect x="35" y="33" width="2" height="4" fill="white" transform="rotate(-45 36 35)"/>
  <circle cx="24" cy="24" r="5" fill="white"/>
  <polygon points="30,20 34,24 30,28" fill="white" opacity="0.8"/>
  <polygon points="18,20 14,24 18,28" fill="white" opacity="0.8"/>
</svg>
