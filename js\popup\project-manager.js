/**
 * WebFlow Automator Pro - Project Manager
 * Handles project creation, management, and organization
 */

class ProjectManager {
  constructor() {
    this.projects = [];
    this.currentProject = null;
    this.init();
  }

  init() {
    this.loadProjects();
  }

  async loadProjects() {
    try {
      const data = await StorageUtils.get(['projects', 'currentProject']);
      this.projects = data.projects || [];
      this.currentProject = data.currentProject || null;
    } catch (error) {
      console.error('Failed to load projects:', error);
    }
  }

  async saveProjects() {
    try {
      await StorageUtils.set({
        projects: this.projects,
        currentProject: this.currentProject
      });
    } catch (error) {
      console.error('Failed to save projects:', error);
    }
  }

  createProject(name, description = '') {
    const project = {
      id: StringUtils.generateId(),
      name: name,
      description: description,
      actions: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tags: [],
      settings: {
        speed: 'normal',
        errorHandling: 'continue',
        highlightElements: true
      }
    };

    this.projects.push(project);
    this.currentProject = project.id;
    this.saveProjects();
    
    return project;
  }

  deleteProject(projectId) {
    const index = this.projects.findIndex(p => p.id === projectId);
    if (index !== -1) {
      this.projects.splice(index, 1);
      
      if (this.currentProject === projectId) {
        this.currentProject = this.projects.length > 0 ? this.projects[0].id : null;
      }
      
      this.saveProjects();
      return true;
    }
    return false;
  }

  updateProject(projectId, updates) {
    const project = this.projects.find(p => p.id === projectId);
    if (project) {
      Object.assign(project, updates);
      project.updatedAt = new Date().toISOString();
      this.saveProjects();
      return project;
    }
    return null;
  }

  getProject(projectId) {
    return this.projects.find(p => p.id === projectId);
  }

  getCurrentProject() {
    return this.currentProject ? this.getProject(this.currentProject) : null;
  }

  setCurrentProject(projectId) {
    if (this.projects.find(p => p.id === projectId)) {
      this.currentProject = projectId;
      this.saveProjects();
      return true;
    }
    return false;
  }

  addActionToProject(projectId, action) {
    const project = this.getProject(projectId);
    if (project) {
      action.id = action.id || StringUtils.generateId();
      project.actions.push(action);
      project.updatedAt = new Date().toISOString();
      this.saveProjects();
      return true;
    }
    return false;
  }

  removeActionFromProject(projectId, actionId) {
    const project = this.getProject(projectId);
    if (project) {
      const index = project.actions.findIndex(a => a.id === actionId);
      if (index !== -1) {
        project.actions.splice(index, 1);
        project.updatedAt = new Date().toISOString();
        this.saveProjects();
        return true;
      }
    }
    return false;
  }

  exportProject(projectId) {
    const project = this.getProject(projectId);
    if (!project) return null;

    const exportData = {
      version: '2.0.0',
      exportDate: new Date().toISOString(),
      project: project
    };

    return JSON.stringify(exportData, null, 2);
  }

  importProject(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      
      if (!data.project || !data.project.actions) {
        throw new Error('Invalid project format');
      }

      const importedProject = {
        ...data.project,
        id: StringUtils.generateId(),
        name: `${data.project.name} (مستورد)`,
        importedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.projects.push(importedProject);
      this.saveProjects();
      
      return importedProject;
    } catch (error) {
      throw new Error('Failed to import project: ' + error.message);
    }
  }

  duplicateProject(projectId) {
    const project = this.getProject(projectId);
    if (!project) return null;

    const duplicatedProject = {
      ...project,
      id: StringUtils.generateId(),
      name: `${project.name} (نسخة)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.projects.push(duplicatedProject);
    this.saveProjects();
    
    return duplicatedProject;
  }

  searchProjects(query) {
    const lowercaseQuery = query.toLowerCase();
    return this.projects.filter(project => 
      project.name.toLowerCase().includes(lowercaseQuery) ||
      project.description.toLowerCase().includes(lowercaseQuery) ||
      project.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  }

  getProjectsByTag(tag) {
    return this.projects.filter(project => 
      project.tags.includes(tag)
    );
  }

  addTagToProject(projectId, tag) {
    const project = this.getProject(projectId);
    if (project && !project.tags.includes(tag)) {
      project.tags.push(tag);
      project.updatedAt = new Date().toISOString();
      this.saveProjects();
      return true;
    }
    return false;
  }

  removeTagFromProject(projectId, tag) {
    const project = this.getProject(projectId);
    if (project) {
      const index = project.tags.indexOf(tag);
      if (index !== -1) {
        project.tags.splice(index, 1);
        project.updatedAt = new Date().toISOString();
        this.saveProjects();
        return true;
      }
    }
    return false;
  }

  getAllTags() {
    const tags = new Set();
    this.projects.forEach(project => {
      project.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  }

  getProjectStats() {
    const totalProjects = this.projects.length;
    const totalActions = this.projects.reduce((sum, project) => sum + project.actions.length, 0);
    const avgActionsPerProject = totalProjects > 0 ? Math.round(totalActions / totalProjects) : 0;
    
    return {
      totalProjects,
      totalActions,
      avgActionsPerProject,
      tags: this.getAllTags().length
    };
  }

  renderProjectsList() {
    const container = document.getElementById('projectsList');
    if (!container) return;

    if (this.projects.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <i class="icon-folder"></i>
          <h4>لا توجد مشاريع</h4>
          <p>أنشئ مشروعك الأول لحفظ وتنظيم الإجراءات</p>
        </div>
      `;
      return;
    }

    const projectsHTML = this.projects.map(project => `
      <div class="project-card ${this.currentProject === project.id ? 'active' : ''}" data-project-id="${project.id}">
        <div class="project-header">
          <h4 class="project-name">${project.name}</h4>
          <div class="project-actions">
            <button class="btn-icon" onclick="projectManager.editProject('${project.id}')" title="تعديل">
              <i class="icon-edit"></i>
            </button>
            <button class="btn-icon" onclick="projectManager.duplicateProject('${project.id}')" title="نسخ">
              <i class="icon-copy"></i>
            </button>
            <button class="btn-icon" onclick="projectManager.exportProject('${project.id}')" title="تصدير">
              <i class="icon-export"></i>
            </button>
            <button class="btn-icon" onclick="projectManager.deleteProject('${project.id}')" title="حذف">
              <i class="icon-trash"></i>
            </button>
          </div>
        </div>
        
        <div class="project-description">
          ${project.description || 'لا يوجد وصف'}
        </div>
        
        <div class="project-stats">
          <div class="stat">
            <span class="stat-value">${project.actions.length}</span>
            <span class="stat-label">إجراء</span>
          </div>
          <div class="stat">
            <span class="stat-value">${StringUtils.formatDate(project.updatedAt)}</span>
            <span class="stat-label">آخر تحديث</span>
          </div>
        </div>
        
        <div class="project-tags">
          ${project.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
        </div>
        
        <div class="project-footer">
          <button class="btn btn-outline" onclick="projectManager.setCurrentProject('${project.id}')">
            ${this.currentProject === project.id ? 'المشروع الحالي' : 'تحديد كحالي'}
          </button>
          <button class="btn btn-primary" onclick="projectManager.loadProject('${project.id}')">
            تحميل
          </button>
        </div>
      </div>
    `).join('');

    container.innerHTML = projectsHTML;
  }

  editProject(projectId) {
    // Implementation for project editing
    console.log('Edit project:', projectId);
  }

  loadProject(projectId) {
    const project = this.getProject(projectId);
    if (project && window.uiManager) {
      window.uiManager.actions = [...project.actions];
      window.uiManager.renderActionsList();
      this.setCurrentProject(projectId);
      window.uiManager.switchTab('actions');
      window.uiManager.showNotification(`تم تحميل مشروع "${project.name}"`, 'success');
    }
  }
}

// Export for global use
if (typeof window !== 'undefined') {
  window.ProjectManager = ProjectManager;
}
