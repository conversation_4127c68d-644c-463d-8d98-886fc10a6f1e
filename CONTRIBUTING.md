# دليل المساهمة في WebFlow Automator Pro

نرحب بمساهماتكم في تطوير WebFlow Automator Pro! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 📋 جدول المحتويات

- [قواعد السلوك](#قواعد-السلوك)
- [كيفية المساهمة](#كيفية-المساهمة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح ميزات جديدة](#اقتراح-ميزات-جديدة)
- [إرشادات التطوير](#إرشادات-التطوير)
- [عملية المراجعة](#عملية-المراجعة)
- [الحصول على المساعدة](#الحصول-على-المساعدة)

## 🤝 قواعد السلوك

### تعهدنا
نحن كمساهمين ومشرفين نتعهد بجعل المشاركة في مشروعنا ومجتمعنا تجربة خالية من التحرش للجميع، بغض النظر عن العمر أو الجنس أو الهوية الجنسية أو التعبير عنها أو التوجه الجنسي أو الإعاقة أو المظهر الشخصي أو حجم الجسم أو العرق أو الدين أو مستوى الخبرة.

### معاييرنا
أمثلة على السلوك الذي يساهم في خلق بيئة إيجابية:
- استخدام لغة ترحيبية وشاملة
- احترام وجهات النظر والخبرات المختلفة
- قبول النقد البناء بأدب
- التركيز على ما هو أفضل للمجتمع
- إظهار التعاطف تجاه أعضاء المجتمع الآخرين

## 🚀 كيفية المساهمة

### 1. إعداد بيئة التطوير

```bash
# استنساخ المستودع
git clone https://github.com/webflow-team/webflow-automator-pro.git
cd webflow-automator-pro

# إنشاء فرع جديد للميزة
git checkout -b feature/اسم-الميزة

# تثبيت التبعيات (إذا كانت موجودة)
npm install
```

### 2. إجراء التغييرات

1. **اكتب كود نظيف ومفهوم**
2. **أضف تعليقات باللغة العربية**
3. **اتبع إرشادات التصميم الموجودة**
4. **اختبر تغييراتك بدقة**

### 3. إرسال Pull Request

1. **ادفع تغييراتك إلى فرعك**
```bash
git add .
git commit -m "إضافة: وصف مختصر للتغيير"
git push origin feature/اسم-الميزة
```

2. **أنشئ Pull Request على GitHub**
3. **اكتب وصفاً واضحاً للتغييرات**
4. **اربط أي Issues ذات صلة**

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ عن خطأ
- تأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
- تحقق من أنك تستخدم أحدث إصدار
- جرب إعادة إنتاج الخطأ في بيئة نظيفة

### كيفية كتابة تقرير خطأ جيد

استخدم القالب التالي:

```markdown
## وصف الخطأ
وصف واضح ومختصر للخطأ.

## خطوات إعادة الإنتاج
1. اذهب إلى '...'
2. انقر على '...'
3. مرر إلى '...'
4. شاهد الخطأ

## السلوك المتوقع
وصف واضح لما كنت تتوقع حدوثه.

## لقطات الشاشة
إذا كان ذلك مناسباً، أضف لقطات شاشة لتوضيح المشكلة.

## معلومات البيئة
- نظام التشغيل: [مثل Windows 10, macOS 11.0]
- المتصفح: [مثل Chrome 96.0]
- إصدار الإضافة: [مثل 2.0.0]

## معلومات إضافية
أي معلومات أخرى حول المشكلة.
```

## 💡 اقتراح ميزات جديدة

### قبل اقتراح ميزة
- تحقق من أن الميزة لم يتم اقتراحها مسبقاً
- فكر في كيفية استفادة المستخدمين من هذه الميزة
- تأكد من أن الميزة تتماشى مع أهداف المشروع

### كيفية كتابة اقتراح ميزة جيد

```markdown
## ملخص الميزة
وصف مختصر للميزة المقترحة.

## المشكلة التي تحلها
وصف المشكلة أو الحاجة التي تعالجها هذه الميزة.

## الحل المقترح
وصف مفصل لكيفية عمل الميزة.

## البدائل المدروسة
وصف أي حلول أو ميزات بديلة فكرت فيها.

## معلومات إضافية
أي معلومات أخرى أو لقطات شاشة حول اقتراح الميزة.
```

## 🛠️ إرشادات التطوير

### معايير الكود

#### JavaScript
```javascript
// استخدم أسماء متغيرات واضحة باللغة العربية أو الإنجليزية
const عددالإجراءات = actions.length;
const actionCount = actions.length; // أو بالإنجليزية

// اكتب تعليقات باللغة العربية
/**
 * تنفيذ إجراء واحد
 * @param {Object} action - الإجراء المراد تنفيذه
 * @returns {Promise<Object>} نتيجة التنفيذ
 */
async function executeAction(action) {
  // تحقق من صحة الإجراء
  if (!action || !action.type) {
    throw new Error('الإجراء غير صحيح');
  }
  
  // تنفيذ الإجراء
  return await performAction(action);
}
```

#### CSS
```css
/* استخدم أسماء فئات واضحة */
.webflow-action-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

/* اكتب تعليقات للأقسام المعقدة */
/* تصميم الأزرار الأساسية */
.btn {
  /* الخصائص الأساسية */
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  
  /* التأثيرات */
  transition: all 0.2s ease;
}
```

#### HTML
```html
<!-- استخدم أسماء معرفات واضحة -->
<div class="action-form" id="actionForm">
  <!-- اكتب تعليقات للأقسام -->
  <!-- قسم اختيار نوع الإجراء -->
  <div class="form-group">
    <label for="actionType">نوع الإجراء</label>
    <select id="actionType" class="form-control">
      <option value="click">نقر</option>
      <option value="type">كتابة</option>
    </select>
  </div>
</div>
```

### بنية الملفات

```
webflow-automator-pro/
├── manifest.json              # إعدادات الإضافة
├── popup.html                 # الواجهة الرئيسية
├── content.js                 # سكريبت المحتوى
├── js/
│   ├── background.js          # سكريبت الخلفية
│   ├── popup.js              # منطق الواجهة الرئيسي
│   ├── utils/
│   │   └── helpers.js        # وظائف مساعدة
│   ├── core/
│   │   ├── element-selector.js    # محدد العناصر
│   │   └── action-executor.js     # منفذ الإجراءات
│   └── popup/
│       ├── ui-manager.js          # مدير الواجهة
│       ├── project-manager.js     # مدير المشاريع
│       └── template-manager.js    # مدير القوالب
├── css/
│   ├── popup.css             # أنماط الواجهة
│   ├── content.css           # أنماط المحتوى
│   └── icons.css             # أيقونات
└── icons/                    # أيقونات الإضافة
```

### اختبار التغييرات

#### اختبار يدوي
1. **حمل الإضافة في Chrome**
```
chrome://extensions/ -> وضع المطور -> تحميل إضافة غير مضغوطة
```

2. **اختبر على صفحة الاختبار**
```
افتح test.html في المتصفح واختبر جميع الوظائف
```

3. **اختبر على مواقع حقيقية**
```
جرب الإضافة على مواقع مختلفة للتأكد من التوافق
```

#### اختبار الأداء
```javascript
// قياس وقت التنفيذ
console.time('تنفيذ الإجراءات');
await executeActions(actions);
console.timeEnd('تنفيذ الإجراءات');

// مراقبة استهلاك الذاكرة
console.log('استهلاك الذاكرة:', performance.memory);
```

### التوثيق

#### تعليقات الكود
```javascript
/**
 * فئة لإدارة تنفيذ الإجراءات
 * تتعامل مع تنفيذ سلسلة من الإجراءات مع معالجة الأخطاء
 */
class ActionExecutor {
  /**
   * إنشاء منفذ إجراءات جديد
   * @param {Object} options - خيارات التكوين
   * @param {string} options.speed - سرعة التنفيذ (slow, normal, fast, instant)
   * @param {string} options.errorHandling - طريقة معالجة الأخطاء (continue, stop)
   */
  constructor(options = {}) {
    // تهيئة الخصائص
  }
}
```

#### README للميزات الجديدة
عند إضافة ميزة جديدة، تأكد من تحديث:
- `README.md` - الوصف العام
- `INSTALLATION.md` - تعليمات الاستخدام
- `CHANGELOG.md` - سجل التغييرات

## 🔍 عملية المراجعة

### معايير القبول
- **الكود نظيف ومفهوم**
- **التعليقات واضحة وباللغة العربية**
- **لا توجد أخطاء في وحدة التحكم**
- **الميزة تعمل كما هو متوقع**
- **لا تؤثر سلباً على الميزات الموجودة**
- **متوافقة مع إرشادات التصميم**

### عملية المراجعة
1. **مراجعة تلقائية** - فحص الكود للأخطاء الأساسية
2. **مراجعة يدوية** - مراجعة من قبل المشرفين
3. **اختبار** - اختبار الميزة في بيئات مختلفة
4. **موافقة** - موافقة نهائية ودمج

### نصائح للحصول على مراجعة سريعة
- **اكتب وصفاً واضحاً للـ PR**
- **قسم التغييرات الكبيرة إلى PRs أصغر**
- **اختبر تغييراتك بدقة قبل الإرسال**
- **رد على تعليقات المراجعين بسرعة**

## 📞 الحصول على المساعدة

### قنوات التواصل
- **GitHub Issues** - للأسئلة التقنية والأخطاء
- **GitHub Discussions** - للنقاشات العامة
- **Discord** - للدردشة المباشرة (قريباً)
- **البريد الإلكتروني** - <EMAIL>

### الموارد المفيدة
- [Chrome Extension Documentation](https://developer.chrome.com/docs/extensions/)
- [Manifest V3 Migration Guide](https://developer.chrome.com/docs/extensions/mv3/intro/)
- [JavaScript Style Guide](https://google.github.io/styleguide/jsguide.html)

## 🏆 الاعتراف بالمساهمين

نقدر جميع المساهمات، مهما كانت صغيرة! سيتم إضافة جميع المساهمين إلى:
- قائمة المساهمين في README
- صفحة الشكر في الإضافة
- سجل التغييرات

### أنواع المساهمات المقدرة
- 💻 **الكود** - كتابة وتحسين الكود
- 🐛 **الأخطاء** - الإبلاغ عن الأخطاء واختبارها
- 📖 **التوثيق** - كتابة وتحسين الوثائق
- 🎨 **التصميم** - تحسين واجهة المستخدم
- 💡 **الأفكار** - اقتراح ميزات جديدة
- 🌍 **الترجمة** - ترجمة الإضافة للغات أخرى
- 📢 **التسويق** - نشر الكلمة عن المشروع

---

**شكراً لكم على اهتمامكم بالمساهمة في WebFlow Automator Pro! 🚀**

معاً نبني أفضل أداة لأتمتة المواقع باللغة العربية!
