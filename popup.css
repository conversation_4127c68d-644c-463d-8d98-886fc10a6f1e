/**
 * أداة الأتمتة الذكية - تصميم الواجهة
 * تصميم بسيط وجميل للإضافة
 */

/* إعدادات أساسية */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  width: 400px;
  min-height: 500px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  direction: rtl;
  overflow-x: hidden;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  font-size: 24px;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0;
}

/* Main Content */
.main-content {
  padding: 20px;
  background: white;
  min-height: 400px;
}

/* Sections */
.section {
  margin-bottom: 25px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 8px;
}

/* Recording Status */
.recording-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 20px;
  background: #e9ecef;
  font-size: 12px;
  font-weight: 500;
}

.recording-status.active {
  background: #dc3545;
  color: white;
  animation: pulse 2s infinite;
}

.recording-status.active::before {
  content: '🔴';
  animation: blink 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* Buttons */
.btn {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

.btn-secondary {
  background: #6c757d;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-success {
  background: #28a745;
}

.btn-success:hover {
  background: #218838;
}

.btn-outline {
  background: transparent;
  border: 2px solid #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background: #6c757d;
  color: white;
}

/* Recording Controls */
.recording-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 15px;
}

.recording-controls .btn {
  flex: 1;
}

/* Recording Info */
.recording-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item .label {
  font-size: 13px;
  color: #6c757d;
}

.info-item .value {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
}

/* Forms */
.action-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.form-group {
  margin-bottom: 15px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 13px;
  background: white;
  color: #495057;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group {
  display: flex;
  gap: 8px;
}

.input-group .form-control {
  flex: 1;
}

.input-group .btn {
  flex-shrink: 0;
  padding: 10px 12px;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.form-actions .btn {
  flex: 1;
}

/* Execution Controls */
.execution-controls {
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  border: 1px solid #dee2e6;
}

/* Actions List */
.actions-list {
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: white;
  min-height: 150px;
  max-height: 250px;
  overflow-y: auto;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f8f9fa;
  transition: all 0.3s ease;
}

.action-item:last-child {
  border-bottom: none;
}

.action-item:hover {
  background: #f8f9fa;
}

.action-item .action-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  font-size: 14px;
}

.action-item .action-content {
  flex: 1;
}

.action-item .action-title {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 2px;
}

.action-item .action-details {
  font-size: 12px;
  color: #6c757d;
}

.action-item .action-controls {
  display: flex;
  gap: 4px;
}

.action-item .btn-small {
  padding: 4px 8px;
  font-size: 11px;
  min-width: auto;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #495057;
}

.empty-state p {
  font-size: 13px;
  line-height: 1.4;
}

/* Footer */
.footer {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 12px 20px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #28a745;
}

.status-dot.error {
  background: #dc3545;
}

.status-dot.warning {
  background: #ffc107;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive */
@media (max-width: 400px) {
  body {
    width: 100%;
    min-width: 320px;
  }
  
  .recording-controls {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
