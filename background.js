/**
 * أداة الأتمتة الذكية - Background Script
 * يدير المهام الخلفية والإعدادات
 */

// تثبيت الإضافة
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('تم تثبيت أداة الأتمتة الذكية بنجاح');
    
    // إعداد الإعدادات الافتراضية
    chrome.storage.local.set({
      actions: [],
      settings: {
        speed: 'normal',
        autoSave: true,
        showNotifications: true
      }
    });
  }
});

// التعامل مع الرسائل
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'saveActions':
      chrome.storage.local.set({ actions: request.actions }, () => {
        sendResponse({ success: true });
      });
      return true;
      
    case 'getActions':
      chrome.storage.local.get(['actions'], (result) => {
        sendResponse({ actions: result.actions || [] });
      });
      return true;
      
    case 'clearActions':
      chrome.storage.local.set({ actions: [] }, () => {
        sendResponse({ success: true });
      });
      return true;
      
    default:
      sendResponse({ error: 'Unknown action' });
  }
});

// إضافة قائمة السياق
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'start-recording',
    title: 'بدء تسجيل الإجراءات',
    contexts: ['page']
  });
  
  chrome.contextMenus.create({
    id: 'select-element',
    title: 'تحديد هذا العنصر',
    contexts: ['all']
  });
});

// التعامل مع نقرات قائمة السياق
chrome.contextMenus.onClicked.addListener((info, tab) => {
  switch (info.menuItemId) {
    case 'start-recording':
      chrome.tabs.sendMessage(tab.id, { action: 'startRecording' });
      break;
    case 'select-element':
      chrome.tabs.sendMessage(tab.id, { action: 'selectElement' });
      break;
  }
});
