/**
 * WebFlow Automator Pro - Template Manager
 * Handles pre-built templates for common automation tasks
 */

class TemplateManager {
  constructor() {
    this.templates = [];
    this.categories = [];
    this.init();
  }

  init() {
    this.loadBuiltInTemplates();
    this.loadCustomTemplates();
  }

  loadBuiltInTemplates() {
    this.templates = [
      {
        id: 'login-form',
        name: 'تسجيل الدخول',
        description: 'قالب لتسجيل الدخول في المواقع',
        category: 'authentication',
        icon: 'icon-user',
        actions: [
          {
            type: 'type',
            selector: 'input[type="email"], input[name="email"], #email',
            value: '{{email}}',
            waitBefore: 500
          },
          {
            type: 'type',
            selector: 'input[type="password"], input[name="password"], #password',
            value: '{{password}}',
            waitBefore: 500
          },
          {
            type: 'click',
            selector: 'button[type="submit"], input[type="submit"], .login-btn',
            waitBefore: 1000
          }
        ],
        variables: ['email', 'password'],
        tags: ['login', 'authentication', 'form'],
        builtin: true
      },
      {
        id: 'contact-form',
        name: 'نموذج اتصال',
        description: 'قالب لملء نماذج الاتصال',
        category: 'forms',
        icon: 'icon-mail',
        actions: [
          {
            type: 'type',
            selector: 'input[name="name"], #name, .name-field',
            value: '{{name}}',
            waitBefore: 300
          },
          {
            type: 'type',
            selector: 'input[name="email"], #email, .email-field',
            value: '{{email}}',
            waitBefore: 300
          },
          {
            type: 'type',
            selector: 'input[name="phone"], #phone, .phone-field',
            value: '{{phone}}',
            waitBefore: 300
          },
          {
            type: 'type',
            selector: 'textarea[name="message"], #message, .message-field',
            value: '{{message}}',
            waitBefore: 300
          },
          {
            type: 'click',
            selector: 'button[type="submit"], input[type="submit"], .submit-btn',
            waitBefore: 1000
          }
        ],
        variables: ['name', 'email', 'phone', 'message'],
        tags: ['contact', 'form', 'communication'],
        builtin: true
      },
      {
        id: 'search-and-filter',
        name: 'البحث والتصفية',
        description: 'قالب للبحث وتطبيق المرشحات',
        category: 'navigation',
        icon: 'icon-search',
        actions: [
          {
            type: 'type',
            selector: 'input[type="search"], .search-input, #search',
            value: '{{searchTerm}}',
            waitBefore: 500
          },
          {
            type: 'click',
            selector: '.search-btn, button[type="submit"]',
            waitBefore: 1000
          },
          {
            type: 'wait',
            duration: 2000
          },
          {
            type: 'click',
            selector: '{{filterSelector}}',
            waitBefore: 500
          }
        ],
        variables: ['searchTerm', 'filterSelector'],
        tags: ['search', 'filter', 'navigation'],
        builtin: true
      },
      {
        id: 'social-media-post',
        name: 'منشور وسائل التواصل',
        description: 'قالب لنشر المحتوى على وسائل التواصل',
        category: 'social',
        icon: 'icon-heart',
        actions: [
          {
            type: 'click',
            selector: '.compose-btn, .new-post-btn, [aria-label*="compose"]',
            waitBefore: 1000
          },
          {
            type: 'type',
            selector: 'textarea, .compose-text, [contenteditable="true"]',
            value: '{{postContent}}',
            waitBefore: 500
          },
          {
            type: 'click',
            selector: '.post-btn, .publish-btn, .tweet-btn',
            waitBefore: 1000
          }
        ],
        variables: ['postContent'],
        tags: ['social', 'post', 'content'],
        builtin: true
      },
      {
        id: 'data-extraction',
        name: 'استخراج البيانات',
        description: 'قالب لاستخراج البيانات من الجداول والقوائم',
        category: 'data',
        icon: 'icon-extract',
        actions: [
          {
            type: 'extract',
            selector: '{{tableSelector}} tr',
            attribute: 'text',
            variable: 'tableData',
            waitBefore: 1000
          },
          {
            type: 'scroll',
            direction: 'down',
            amount: 500,
            waitBefore: 1000
          },
          {
            type: 'condition',
            condition: 'element_exists',
            selector: '.next-page, .pagination-next',
            trueActions: [
              {
                type: 'click',
                selector: '.next-page, .pagination-next',
                waitBefore: 2000
              }
            ]
          }
        ],
        variables: ['tableSelector'],
        tags: ['data', 'extraction', 'scraping'],
        builtin: true
      },
      {
        id: 'e-commerce-checkout',
        name: 'إتمام الشراء',
        description: 'قالب لإتمام عملية الشراء في المتاجر الإلكترونية',
        category: 'ecommerce',
        icon: 'icon-shopping',
        actions: [
          {
            type: 'click',
            selector: '.add-to-cart, .buy-now, [data-action="add-to-cart"]',
            waitBefore: 1000
          },
          {
            type: 'click',
            selector: '.cart-icon, .checkout-btn, .proceed-checkout',
            waitBefore: 2000
          },
          {
            type: 'type',
            selector: 'input[name="email"], #email',
            value: '{{email}}',
            waitBefore: 500
          },
          {
            type: 'type',
            selector: 'input[name="firstName"], #firstName',
            value: '{{firstName}}',
            waitBefore: 300
          },
          {
            type: 'type',
            selector: 'input[name="lastName"], #lastName',
            value: '{{lastName}}',
            waitBefore: 300
          },
          {
            type: 'type',
            selector: 'input[name="address"], #address',
            value: '{{address}}',
            waitBefore: 300
          }
        ],
        variables: ['email', 'firstName', 'lastName', 'address'],
        tags: ['ecommerce', 'checkout', 'shopping'],
        builtin: true
      }
    ];

    this.categories = [
      { id: 'authentication', name: 'المصادقة', icon: 'icon-lock' },
      { id: 'forms', name: 'النماذج', icon: 'icon-edit' },
      { id: 'navigation', name: 'التنقل', icon: 'icon-compass' },
      { id: 'social', name: 'وسائل التواصل', icon: 'icon-users' },
      { id: 'data', name: 'البيانات', icon: 'icon-extract' },
      { id: 'ecommerce', name: 'التجارة الإلكترونية', icon: 'icon-shopping' }
    ];
  }

  async loadCustomTemplates() {
    try {
      const data = await StorageUtils.get(['customTemplates']);
      const customTemplates = data.customTemplates || [];
      this.templates = [...this.templates, ...customTemplates];
    } catch (error) {
      console.error('Failed to load custom templates:', error);
    }
  }

  async saveCustomTemplates() {
    try {
      const customTemplates = this.templates.filter(t => !t.builtin);
      await StorageUtils.set({ customTemplates });
    } catch (error) {
      console.error('Failed to save custom templates:', error);
    }
  }

  createTemplate(name, description, category, actions, variables = []) {
    const template = {
      id: StringUtils.generateId(),
      name: name,
      description: description,
      category: category,
      icon: 'icon-template',
      actions: actions,
      variables: variables,
      tags: [],
      builtin: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.templates.push(template);
    this.saveCustomTemplates();
    
    return template;
  }

  deleteTemplate(templateId) {
    const template = this.templates.find(t => t.id === templateId);
    if (template && !template.builtin) {
      const index = this.templates.findIndex(t => t.id === templateId);
      this.templates.splice(index, 1);
      this.saveCustomTemplates();
      return true;
    }
    return false;
  }

  updateTemplate(templateId, updates) {
    const template = this.templates.find(t => t.id === templateId);
    if (template && !template.builtin) {
      Object.assign(template, updates);
      template.updatedAt = new Date().toISOString();
      this.saveCustomTemplates();
      return template;
    }
    return null;
  }

  getTemplate(templateId) {
    return this.templates.find(t => t.id === templateId);
  }

  getTemplatesByCategory(category) {
    return this.templates.filter(t => t.category === category);
  }

  searchTemplates(query) {
    const lowercaseQuery = query.toLowerCase();
    return this.templates.filter(template => 
      template.name.toLowerCase().includes(lowercaseQuery) ||
      template.description.toLowerCase().includes(lowercaseQuery) ||
      template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  }

  applyTemplate(templateId, variableValues = {}) {
    const template = this.getTemplate(templateId);
    if (!template) return null;

    // Clone actions and replace variables
    const actions = template.actions.map(action => {
      const newAction = { ...action };
      
      // Replace variables in all string properties
      Object.keys(newAction).forEach(key => {
        if (typeof newAction[key] === 'string') {
          newAction[key] = this.replaceVariables(newAction[key], variableValues);
        }
      });
      
      return newAction;
    });

    return actions;
  }

  replaceVariables(str, variables) {
    return str.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
      return variables[varName] || match;
    });
  }

  exportTemplate(templateId) {
    const template = this.getTemplate(templateId);
    if (!template) return null;

    const exportData = {
      version: '2.0.0',
      exportDate: new Date().toISOString(),
      template: template
    };

    return JSON.stringify(exportData, null, 2);
  }

  importTemplate(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      
      if (!data.template || !data.template.actions) {
        throw new Error('Invalid template format');
      }

      const importedTemplate = {
        ...data.template,
        id: StringUtils.generateId(),
        name: `${data.template.name} (مستورد)`,
        builtin: false,
        importedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.templates.push(importedTemplate);
      this.saveCustomTemplates();
      
      return importedTemplate;
    } catch (error) {
      throw new Error('Failed to import template: ' + error.message);
    }
  }

  renderTemplatesGrid() {
    const container = document.getElementById('templatesGrid');
    if (!container) return;

    if (this.templates.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <i class="icon-template"></i>
          <h4>لا توجد قوالب</h4>
          <p>سيتم إضافة قوالب جاهزة للمواقع الشائعة قريباً</p>
        </div>
      `;
      return;
    }

    // Group templates by category
    const groupedTemplates = this.templates.reduce((groups, template) => {
      const category = template.category || 'other';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(template);
      return groups;
    }, {});

    let templatesHTML = '';

    Object.keys(groupedTemplates).forEach(categoryId => {
      const category = this.categories.find(c => c.id === categoryId) || 
                     { id: categoryId, name: categoryId, icon: 'icon-template' };
      
      templatesHTML += `
        <div class="template-category">
          <div class="category-header">
            <i class="${category.icon}"></i>
            <h4>${category.name}</h4>
          </div>
          <div class="templates-row">
            ${groupedTemplates[categoryId].map(template => `
              <div class="template-card" data-template-id="${template.id}">
                <div class="template-icon">
                  <i class="${template.icon}"></i>
                </div>
                <div class="template-content">
                  <h5 class="template-name">${template.name}</h5>
                  <p class="template-description">${template.description}</p>
                  <div class="template-stats">
                    <span class="stat">
                      <i class="icon-list"></i>
                      ${template.actions.length} إجراء
                    </span>
                    ${template.variables.length > 0 ? `
                      <span class="stat">
                        <i class="icon-variable"></i>
                        ${template.variables.length} متغير
                      </span>
                    ` : ''}
                  </div>
                  <div class="template-tags">
                    ${template.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                  </div>
                </div>
                <div class="template-actions">
                  <button class="btn btn-outline btn-small" onclick="templateManager.previewTemplate('${template.id}')" title="معاينة">
                    <i class="icon-eye"></i>
                  </button>
                  <button class="btn btn-primary btn-small" onclick="templateManager.useTemplate('${template.id}')" title="استخدام">
                    <i class="icon-plus"></i>
                  </button>
                  ${!template.builtin ? `
                    <button class="btn btn-outline btn-small" onclick="templateManager.editTemplate('${template.id}')" title="تعديل">
                      <i class="icon-edit"></i>
                    </button>
                    <button class="btn btn-outline btn-small" onclick="templateManager.deleteTemplate('${template.id}')" title="حذف">
                      <i class="icon-trash"></i>
                    </button>
                  ` : ''}
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    });

    container.innerHTML = templatesHTML;
  }

  previewTemplate(templateId) {
    const template = this.getTemplate(templateId);
    if (!template) return;

    // Show template preview modal
    console.log('Preview template:', template);
  }

  useTemplate(templateId) {
    const template = this.getTemplate(templateId);
    if (!template) return;

    if (template.variables.length > 0) {
      // Show variable input dialog
      this.showVariableDialog(template);
    } else {
      // Apply template directly
      const actions = this.applyTemplate(templateId);
      if (actions && window.uiManager) {
        window.uiManager.actions = [...window.uiManager.actions, ...actions];
        window.uiManager.renderActionsList();
        window.uiManager.switchTab('actions');
        window.uiManager.showNotification(`تم إضافة قالب "${template.name}"`, 'success');
      }
    }
  }

  showVariableDialog(template) {
    // Implementation for variable input dialog
    console.log('Show variable dialog for template:', template);
  }

  editTemplate(templateId) {
    // Implementation for template editing
    console.log('Edit template:', templateId);
  }
}

// Export for global use
if (typeof window !== 'undefined') {
  window.TemplateManager = TemplateManager;
}
