/**
 * WebFlow Automator Pro - Background Service Worker
 * Handles background tasks, notifications, and extension lifecycle
 */

// Extension installation and updates
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    // First time installation
    initializeExtension();
    showWelcomeNotification();
  } else if (details.reason === 'update') {
    // Extension updated
    handleExtensionUpdate(details.previousVersion);
  }
});

// Initialize extension with default settings
async function initializeExtension() {
  const defaultSettings = {
    theme: 'light',
    language: 'ar',
    autoSave: true,
    notifications: true,
    recordingMode: 'smart',
    executionSpeed: 'normal',
    errorHandling: 'continue',
    projects: [],
    templates: []
  };
  
  await chrome.storage.local.set({ settings: defaultSettings });
  console.log('WebFlow Automator Pro initialized successfully');
}

// Show welcome notification
function showWelcomeNotification() {
  chrome.notifications.create({
    type: 'basic',
    iconUrl: 'icons/icon48.png',
    title: 'WebFlow Automator Pro',
    message: 'تم تثبيت الإضافة بنجاح! ابدأ بأتمتة مواقعك الآن.'
  });
}

// Handle extension updates
function handleExtensionUpdate(previousVersion) {
  console.log(`Extension updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
  
  // Migration logic for different versions
  if (previousVersion < '2.0.0') {
    migrateFromV1();
  }
}

// Migrate data from version 1.x
async function migrateFromV1() {
  try {
    const oldData = await chrome.storage.local.get(['actions']);
    if (oldData.actions && oldData.actions.length > 0) {
      // Convert old actions to new project format
      const migratedProject = {
        id: generateId(),
        name: 'مشروع مهاجر من الإصدار السابق',
        description: 'تم ترحيل هذا المشروع تلقائياً من الإصدار السابق',
        actions: oldData.actions,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const projects = [migratedProject];
      await chrome.storage.local.set({ projects });
      
      // Remove old data
      await chrome.storage.local.remove(['actions']);
      
      console.log('Successfully migrated data from v1.x');
    }
  } catch (error) {
    console.error('Error migrating data:', error);
  }
}

// Message handling from content scripts and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'getSettings':
      handleGetSettings(sendResponse);
      break;
    case 'updateSettings':
      handleUpdateSettings(request.settings, sendResponse);
      break;
    case 'exportProject':
      handleExportProject(request.projectId, sendResponse);
      break;
    case 'importProject':
      handleImportProject(request.projectData, sendResponse);
      break;
    case 'showNotification':
      showNotification(request.notification);
      break;
    default:
      console.warn('Unknown action:', request.action);
  }
  
  return true; // Keep message channel open for async response
});

// Get extension settings
async function handleGetSettings(sendResponse) {
  try {
    const data = await chrome.storage.local.get(['settings']);
    sendResponse({ success: true, settings: data.settings });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// Update extension settings
async function handleUpdateSettings(newSettings, sendResponse) {
  try {
    await chrome.storage.local.set({ settings: newSettings });
    sendResponse({ success: true });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// Export project to file
async function handleExportProject(projectId, sendResponse) {
  try {
    const data = await chrome.storage.local.get(['projects']);
    const project = data.projects?.find(p => p.id === projectId);
    
    if (!project) {
      throw new Error('Project not found');
    }
    
    const exportData = {
      version: '2.0.0',
      exportDate: new Date().toISOString(),
      project: project
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    await chrome.downloads.download({
      url: url,
      filename: `webflow-project-${project.name}-${Date.now()}.json`,
      saveAs: true
    });
    
    sendResponse({ success: true });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// Import project from file
async function handleImportProject(projectData, sendResponse) {
  try {
    // Validate project data
    if (!projectData.project || !projectData.project.actions) {
      throw new Error('Invalid project data format');
    }
    
    const data = await chrome.storage.local.get(['projects']);
    const projects = data.projects || [];
    
    // Generate new ID for imported project
    const importedProject = {
      ...projectData.project,
      id: generateId(),
      name: `${projectData.project.name} (مستورد)`,
      importedAt: new Date().toISOString()
    };
    
    projects.push(importedProject);
    await chrome.storage.local.set({ projects });
    
    sendResponse({ success: true, project: importedProject });
  } catch (error) {
    sendResponse({ success: false, error: error.message });
  }
}

// Show notification
function showNotification(notification) {
  chrome.notifications.create({
    type: notification.type || 'basic',
    iconUrl: 'icons/icon48.png',
    title: notification.title,
    message: notification.message
  });
}

// Utility function to generate unique IDs
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Context menu setup
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'webflow-record',
    title: 'بدء تسجيل الإجراءات',
    contexts: ['page']
  });
  
  chrome.contextMenus.create({
    id: 'webflow-element-selector',
    title: 'تحديد هذا العنصر',
    contexts: ['all']
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  switch (info.menuItemId) {
    case 'webflow-record':
      chrome.tabs.sendMessage(tab.id, { action: 'startRecording' });
      break;
    case 'webflow-element-selector':
      chrome.tabs.sendMessage(tab.id, { action: 'selectElement', element: info });
      break;
  }
});

// Tab updates handling
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Inject content script if needed
    chrome.tabs.sendMessage(tabId, { action: 'ping' }).catch(() => {
      // Content script not loaded, inject it
      chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['js/content.js']
      });
    });
  }
});
