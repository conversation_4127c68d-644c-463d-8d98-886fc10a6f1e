/**
 * WebFlow Automator Pro - Icon Font
 * Custom icon font for the extension UI
 */

@font-face {
  font-family: 'WebFlowIcons';
  src: url('data:application/font-woff2;charset=utf-8;base64,') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

/* Base icon class */
[class^="icon-"], [class*=" icon-"] {
  font-family: 'WebFlowIcons', sans-serif;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Icon definitions using Unicode symbols as fallback */
.icon-record::before {
  content: "⏺";
  color: #ff4757;
}

.icon-stop::before {
  content: "⏹";
}

.icon-pause::before {
  content: "⏸";
}

.icon-play::before {
  content: "▶";
  color: #2ed573;
}

.icon-list::before {
  content: "☰";
}

.icon-folder::before {
  content: "📁";
}

.icon-template::before {
  content: "📋";
}

.icon-settings::before {
  content: "⚙";
}

.icon-help::before {
  content: "❓";
}

.icon-plus::before {
  content: "➕";
}

.icon-trash::before {
  content: "🗑";
}

.icon-upload::before {
  content: "⬆";
}

.icon-download::before {
  content: "⬇";
}

.icon-target::before {
  content: "🎯";
}

.icon-close::before {
  content: "✕";
}

.icon-refresh::before {
  content: "🔄";
}

.icon-edit::before {
  content: "✏";
}

.icon-copy::before {
  content: "📋";
}

.icon-save::before {
  content: "💾";
}

.icon-export::before {
  content: "📤";
}

.icon-import::before {
  content: "📥";
}

.icon-search::before {
  content: "🔍";
}

.icon-filter::before {
  content: "🔽";
}

.icon-sort::before {
  content: "↕";
}

.icon-check::before {
  content: "✓";
  color: #2ed573;
}

.icon-error::before {
  content: "✗";
  color: #ff4757;
}

.icon-warning::before {
  content: "⚠";
  color: #ffa502;
}

.icon-info::before {
  content: "ℹ";
  color: #3742fa;
}

.icon-click::before {
  content: "👆";
}

.icon-type::before {
  content: "⌨";
}

.icon-select::before {
  content: "📋";
}

.icon-hover::before {
  content: "👋";
}

.icon-scroll::before {
  content: "📜";
}

.icon-wait::before {
  content: "⏱";
}

.icon-extract::before {
  content: "📊";
}

.icon-condition::before {
  content: "❓";
}

.icon-loop::before {
  content: "🔄";
}

.icon-variable::before {
  content: "📝";
}

.icon-javascript::before {
  content: "🔧";
}

.icon-eye::before {
  content: "👁";
}

.icon-eye-off::before {
  content: "🙈";
}

.icon-lock::before {
  content: "🔒";
}

.icon-unlock::before {
  content: "🔓";
}

.icon-star::before {
  content: "⭐";
}

.icon-heart::before {
  content: "❤";
}

.icon-bookmark::before {
  content: "🔖";
}

.icon-tag::before {
  content: "🏷";
}

.icon-calendar::before {
  content: "📅";
}

.icon-clock::before {
  content: "🕐";
}

.icon-user::before {
  content: "👤";
}

.icon-users::before {
  content: "👥";
}

.icon-home::before {
  content: "🏠";
}

.icon-globe::before {
  content: "🌐";
}

.icon-link::before {
  content: "🔗";
}

.icon-mail::before {
  content: "✉";
}

.icon-phone::before {
  content: "📞";
}

.icon-camera::before {
  content: "📷";
}

.icon-image::before {
  content: "🖼";
}

.icon-video::before {
  content: "🎥";
}

.icon-music::before {
  content: "🎵";
}

.icon-volume::before {
  content: "🔊";
}

.icon-volume-off::before {
  content: "🔇";
}

.icon-battery::before {
  content: "🔋";
}

.icon-wifi::before {
  content: "📶";
}

.icon-bluetooth::before {
  content: "📶";
}

.icon-location::before {
  content: "📍";
}

.icon-map::before {
  content: "🗺";
}

.icon-compass::before {
  content: "🧭";
}

.icon-weather::before {
  content: "🌤";
}

.icon-sun::before {
  content: "☀";
}

.icon-moon::before {
  content: "🌙";
}

.icon-cloud::before {
  content: "☁";
}

.icon-rain::before {
  content: "🌧";
}

.icon-snow::before {
  content: "❄";
}

.icon-fire::before {
  content: "🔥";
}

.icon-water::before {
  content: "💧";
}

.icon-leaf::before {
  content: "🍃";
}

.icon-tree::before {
  content: "🌳";
}

.icon-flower::before {
  content: "🌸";
}

/* Action type specific icons with colors */
.action-icon.click {
  background: #3742fa !important;
}

.action-icon.type {
  background: #2ed573 !important;
}

.action-icon.select {
  background: #ffa502 !important;
}

.action-icon.hover {
  background: #ff6b6b !important;
}

.action-icon.scroll {
  background: #5f27cd !important;
}

.action-icon.wait {
  background: #6c757d !important;
}

.action-icon.extract {
  background: #00d2d3 !important;
}

.action-icon.condition {
  background: #ff9ff3 !important;
}

.action-icon.loop {
  background: #54a0ff !important;
}

.action-icon.variable {
  background: #5f27cd !important;
}

.action-icon.javascript {
  background: #f1c40f !important;
}

/* Size variations */
.icon-xs {
  font-size: 10px;
}

.icon-sm {
  font-size: 12px;
}

.icon-md {
  font-size: 16px;
}

.icon-lg {
  font-size: 20px;
}

.icon-xl {
  font-size: 24px;
}

.icon-2x {
  font-size: 32px;
}

.icon-3x {
  font-size: 48px;
}

/* Animation classes */
.icon-spin {
  animation: icon-spin 2s infinite linear;
}

.icon-pulse {
  animation: icon-pulse 2s infinite;
}

.icon-bounce {
  animation: icon-bounce 1s infinite;
}

.icon-shake {
  animation: icon-shake 0.5s infinite;
}

/* Keyframes for animations */
@keyframes icon-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes icon-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes icon-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes icon-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* Utility classes */
.icon-muted {
  opacity: 0.5;
}

.icon-disabled {
  opacity: 0.3;
  pointer-events: none;
}

.icon-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-clickable:hover {
  transform: scale(1.1);
}

/* Dark theme adjustments */
[data-theme="dark"] .icon-record::before {
  color: #ff6b6b;
}

[data-theme="dark"] .icon-play::before {
  color: #2ed573;
}

[data-theme="dark"] .icon-check::before {
  color: #2ed573;
}

[data-theme="dark"] .icon-error::before {
  color: #ff4757;
}

[data-theme="dark"] .icon-warning::before {
  color: #ffa502;
}

[data-theme="dark"] .icon-info::before {
  color: #3742fa;
}
