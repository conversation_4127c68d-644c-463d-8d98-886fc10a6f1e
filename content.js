/**
 * أداة الأتمتة الذكية - Content Script
 * يتعامل مع الصفحات وينفذ الإجراءات
 */

// متغيرات عامة
let isRecording = false;
let recordedActions = [];
let isSelecting = false;

// تهيئة السكريبت
console.log('تم تحميل أداة الأتمتة الذكية في الصفحة');

// الاستماع للرسائل من الإضافة
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  try {
    switch (request.action) {
      case 'ping':
        sendResponse({ status: 'ready' });
        break;

      case 'startRecording':
        handleStartRecording(sendResponse);
        break;

      case 'stopRecording':
        handleStopRecording(sendResponse);
        break;

      case 'selectElement':
        handleSelectElement(sendResponse);
        break;

      case 'executeActions':
        handleExecuteActions(request, sendResponse);
        break;

      default:
        sendResponse({ error: 'Unknown action' });
    }
  } catch (error) {
    console.error('خطأ في معالجة الرسالة:', error);
    sendResponse({ error: error.message });
  }

  return true; // الحفاظ على قناة الاتصال مفتوحة
});

/**
 * بدء تسجيل الإجراءات
 */
function handleStartRecording(sendResponse) {
  if (isRecording) {
    sendResponse({ error: 'التسجيل قيد التشغيل بالفعل' });
    return;
  }

  isRecording = true;
  recordedActions = [];

  // بدء تسجيل الأحداث
  startEventRecording();

  sendResponse({ success: true, recording: true });
}

/**
 * إيقاف تسجيل الإجراءات
 */
function handleStopRecording(sendResponse) {
  if (!isRecording) {
    sendResponse({ error: 'لا يوجد تسجيل قيد التشغيل' });
    return;
  }

  isRecording = false;

  // إيقاف تسجيل الأحداث
  stopEventRecording();

  sendResponse({
    success: true,
    recording: false,
    actions: recordedActions
  });
}

/**
 * تحديد عنصر من الصفحة
 */
function handleSelectElement(sendResponse) {
  if (isSelecting) {
    sendResponse({ error: 'التحديد قيد التشغيل بالفعل' });
    return;
  }

  isSelecting = true;

  // إنشاء overlay للتحديد
  createSelectionOverlay((element) => {
    isSelecting = false;

    if (element) {
      const selector = generateSelector(element);
      sendResponse({
        success: true,
        selector: selector,
        element: {
          tagName: element.tagName,
          id: element.id,
          className: element.className
        }
      });
    } else {
      sendResponse({ success: false, cancelled: true });
    }
  });
}

/**
 * تنفيذ الإجراءات
 */
async function handleExecuteActions(request, sendResponse) {
  try {
    const actions = request.actions || [];
    const speed = request.speed || 'normal';

    const delays = {
      slow: 2000,
      normal: 1000,
      fast: 500
    };

    const delay = delays[speed] || 1000;

    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];

      // انتظار قبل تنفيذ الإجراء
      if (action.waitTime > 0) {
        await sleep(action.waitTime);
      } else {
        await sleep(delay);
      }

      // تنفيذ الإجراء
      await executeAction(action);
    }

    sendResponse({ success: true, executed: actions.length });
  } catch (error) {
    console.error('خطأ في تنفيذ الإجراءات:', error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * تنفيذ إجراء واحد
 */
async function executeAction(action) {
  const element = document.querySelector(action.selector);

  if (!element && action.type !== 'wait' && action.type !== 'scroll') {
    throw new Error(`العنصر غير موجود: ${action.selector}`);
  }

  switch (action.type) {
    case 'click':
      element.click();
      break;

    case 'type':
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        element.focus();
        element.value = action.value || '';
        element.dispatchEvent(new Event('input', { bubbles: true }));
      }
      break;

    case 'wait':
      await sleep(parseInt(action.value) || 1000);
      break;

    case 'scroll':
      const direction = action.value || 'down';
      const scrollAmount = 300;

      switch (direction) {
        case 'up':
          window.scrollBy(0, -scrollAmount);
          break;
        case 'down':
          window.scrollBy(0, scrollAmount);
          break;
        case 'left':
          window.scrollBy(-scrollAmount, 0);
          break;
        case 'right':
          window.scrollBy(scrollAmount, 0);
          break;
      }
      break;

    default:
      console.warn('نوع إجراء غير مدعوم:', action.type);
  }
}

/**
 * وظيفة النوم (انتظار)
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * توليد محدد CSS للعنصر
 */
function generateSelector(element) {
  // إذا كان للعنصر ID فريد
  if (element.id) {
    return '#' + element.id;
  }

  // إذا كان للعنصر فئات CSS
  if (element.className && typeof element.className === 'string') {
    const classes = element.className.split(' ').filter(c => c.trim());
    if (classes.length > 0) {
      return '.' + classes.join('.');
    }
  }

  // محدد بناءً على نوع العنصر والموقع
  let selector = element.tagName.toLowerCase();

  if (element.parentNode) {
    const siblings = Array.from(element.parentNode.children);
    const index = siblings.indexOf(element);
    if (index > -1) {
      selector += `:nth-child(${index + 1})`;
    }
  }

  return selector;
}

/**
 * إنشاء overlay لتحديد العناصر
 */
function createSelectionOverlay(callback) {
  // إنشاء overlay
  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.3) !important;
    z-index: 999999 !important;
    cursor: crosshair !important;
  `;

  // إنشاء مربع التمييز
  const highlightBox = document.createElement('div');
  highlightBox.style.cssText = `
    position: absolute !important;
    border: 3px solid #ff6b6b !important;
    background: rgba(255, 107, 107, 0.1) !important;
    pointer-events: none !important;
    z-index: 1000000 !important;
    display: none !important;
    border-radius: 4px !important;
  `;

  // إنشاء نص التعليمات
  const instructions = document.createElement('div');
  instructions.style.cssText = `
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: #333 !important;
    color: white !important;
    padding: 15px 25px !important;
    border-radius: 8px !important;
    font-family: Arial, sans-serif !important;
    font-size: 14px !important;
    z-index: 1000001 !important;
    text-align: center !important;
  `;
  instructions.textContent = 'انقر على العنصر المراد تحديده، أو اضغط Escape للإلغاء';

  document.body.appendChild(overlay);
  document.body.appendChild(highlightBox);
  document.body.appendChild(instructions);

  let selectedElement = null;

  // معالج حركة الماوس
  function handleMouseMove(event) {
    const element = document.elementFromPoint(event.clientX, event.clientY);

    if (element && element !== overlay && element !== highlightBox && element !== instructions) {
      const rect = element.getBoundingClientRect();

      highlightBox.style.display = 'block';
      highlightBox.style.left = rect.left + 'px';
      highlightBox.style.top = rect.top + 'px';
      highlightBox.style.width = rect.width + 'px';
      highlightBox.style.height = rect.height + 'px';

      selectedElement = element;
    } else {
      highlightBox.style.display = 'none';
      selectedElement = null;
    }
  }

  // معالج النقر
  function handleClick(event) {
    event.preventDefault();
    event.stopPropagation();

    cleanup();
    callback(selectedElement);
  }

  // معالج لوحة المفاتيح
  function handleKeydown(event) {
    if (event.key === 'Escape') {
      cleanup();
      callback(null);
    }
  }

  // تنظيف العناصر
  function cleanup() {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('click', handleClick);
    document.removeEventListener('keydown', handleKeydown);

    if (overlay.parentNode) overlay.remove();
    if (highlightBox.parentNode) highlightBox.remove();
    if (instructions.parentNode) instructions.remove();
  }

  // ربط الأحداث
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('click', handleClick);
  document.addEventListener('keydown', handleKeydown);
}

/**
 * بدء تسجيل الأحداث
 */
let eventListeners = [];

function startEventRecording() {
  // تسجيل النقرات
  const clickHandler = (event) => {
    if (!event.target.closest('.automation-overlay')) {
      const selector = generateSelector(event.target);
      recordedActions.push({
        type: 'click',
        selector: selector,
        timestamp: Date.now()
      });
    }
  };

  // تسجيل الكتابة
  const inputHandler = (event) => {
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      const selector = generateSelector(event.target);
      recordedActions.push({
        type: 'type',
        selector: selector,
        value: event.target.value,
        timestamp: Date.now()
      });
    }
  };

  document.addEventListener('click', clickHandler, true);
  document.addEventListener('input', inputHandler, true);

  eventListeners = [
    { element: document, event: 'click', handler: clickHandler },
    { element: document, event: 'input', handler: inputHandler }
  ];
}

/**
 * إيقاف تسجيل الأحداث
 */
function stopEventRecording() {
  eventListeners.forEach(({ element, event, handler }) => {
    element.removeEventListener(event, handler, true);
  });
  eventListeners = [];
}
