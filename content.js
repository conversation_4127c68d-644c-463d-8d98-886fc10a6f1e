// الاستماع للرسائل من popup.js
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.command === "getSelectedElement") {
    // الحصول على العنصر المحدد
    const element = document.activeElement;
    if (element) {
      sendResponse({
        selector: generateSelector(element)
      });
    } else {
      sendResponse({error: "لم يتم تحديد أي عنصر"});
    }
    return true;
  }
  
  if (request.command === "executeActions") {
    executeActions(request.actions);
    return true;
  }
});

// توليد محدد CSS للعنصر
function generateSelector(element) {
  if (element.id) {
    return '#' + element.id;
  }
  
  if (element.className) {
    const classes = element.className.split(' ').filter(c => c);
    if (classes.length > 0) {
      return '.' + classes.join('.');
    }
  }
  
  // محدد بسيط بناءً على نوع العنصر والموقع
  let selector = element.tagName.toLowerCase();
  if (element.parentNode) {
    const siblings = Array.from(element.parentNode.children);
    const index = siblings.indexOf(element);
    if (index > -1) {
      selector += `:nth-child(${index + 1})`;
    }
  }
  
  return selector;
}

// تنفيذ الإجراءات
async function executeActions(actions) {
  for (const action of actions) {
    // انتظار قبل تنفيذ الإجراء
    if (action.waitTime > 0) {
      await new Promise(resolve => setTimeout(resolve, parseInt(action.waitTime)));
    }
    
    // العثور على العنصر
    const element = document.querySelector(action.selector);
    if (!element) continue;
    
    // تنفيذ الإجراء حسب النوع
    switch (action.type) {
      case 'text':
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
          element.value = action.value || '';
        } else {
          element.textContent = action.value || '';
        }
        break;
      case 'click':
        element.click();
        break;
      case 'wait':
        await new Promise(resolve => setTimeout(resolve, 1000));
        break;
    }
  }
}
