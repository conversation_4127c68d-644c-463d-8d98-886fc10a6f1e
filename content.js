/**
 * WebFlow Automator Pro - Content Script
 * Main content script that handles communication with popup and executes automation
 */

// Initialize core components
let elementSelector = null;
let actionExecutor = null;
let isRecording = false;
let recordedActions = [];

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}

function initializeContentScript() {
  try {
    elementSelector = new ElementSelector();
    actionExecutor = new ActionExecutor();
    console.log('WebFlow Automator Pro content script initialized');
  } catch (error) {
    console.error('Failed to initialize content script:', error);
  }
}

// Message handling from popup and background
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  try {
    switch (request.action || request.command) {
      case 'ping':
        sendResponse({ status: 'ready' });
        break;

      case 'startElementSelection':
        handleStartElementSelection(request, sendResponse);
        break;

      case 'getSelectedElement':
        handleGetSelectedElement(request, sendResponse);
        break;

      case 'executeActions':
        handleExecuteActions(request, sendResponse);
        break;

      case 'startRecording':
        handleStartRecording(request, sendResponse);
        break;

      case 'stopRecording':
        handleStopRecording(request, sendResponse);
        break;

      case 'getPageInfo':
        handleGetPageInfo(request, sendResponse);
        break;

      case 'highlightElement':
        handleHighlightElement(request, sendResponse);
        break;

      case 'extractData':
        handleExtractData(request, sendResponse);
        break;

      default:
        console.warn('Unknown action:', request.action || request.command);
        sendResponse({ error: 'Unknown action' });
    }
  } catch (error) {
    console.error('Error handling message:', error);
    sendResponse({ error: error.message });
  }

  return true; // Keep message channel open for async responses
});

// Handle element selection start
function handleStartElementSelection(request, sendResponse) {
  if (!elementSelector) {
    sendResponse({ error: 'Element selector not initialized' });
    return;
  }

  elementSelector.startSelection((result) => {
    if (result) {
      sendResponse({
        success: true,
        selector: result.selector,
        element: {
          tagName: result.element.tagName,
          id: result.element.id,
          className: result.element.className,
          textContent: result.element.textContent?.substring(0, 100)
        },
        info: result.info
      });
    } else {
      sendResponse({ success: false, cancelled: true });
    }
  });
}

// Handle get selected element (legacy support)
function handleGetSelectedElement(request, sendResponse) {
  const element = document.activeElement;
  if (element && element !== document.body) {
    const selector = generateLegacySelector(element);
    sendResponse({
      selector: selector,
      element: {
        tagName: element.tagName,
        id: element.id,
        className: element.className
      }
    });
  } else {
    sendResponse({ error: "لم يتم تحديد أي عنصر" });
  }
}

// Handle action execution
async function handleExecuteActions(request, sendResponse) {
  if (!actionExecutor) {
    sendResponse({ error: 'Action executor not initialized' });
    return;
  }

  try {
    const result = await actionExecutor.executeActions(request.actions, request.options);
    sendResponse(result);
  } catch (error) {
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Handle recording start
function handleStartRecording(request, sendResponse) {
  if (isRecording) {
    sendResponse({ error: 'Recording already in progress' });
    return;
  }

  isRecording = true;
  recordedActions = [];

  // Start recording user interactions
  startInteractionRecording();

  sendResponse({ success: true, recording: true });
}

// Handle recording stop
function handleStopRecording(request, sendResponse) {
  if (!isRecording) {
    sendResponse({ error: 'No recording in progress' });
    return;
  }

  isRecording = false;

  // Stop recording user interactions
  stopInteractionRecording();

  sendResponse({
    success: true,
    recording: false,
    actions: recordedActions
  });
}

// Handle page info extraction
function handleGetPageInfo(request, sendResponse) {
  const pageInfo = {
    url: window.location.href,
    title: document.title,
    domain: window.location.hostname,
    forms: document.forms.length,
    links: document.links.length,
    images: document.images.length,
    scripts: document.scripts.length,
    meta: {
      description: document.querySelector('meta[name="description"]')?.content || '',
      keywords: document.querySelector('meta[name="keywords"]')?.content || '',
      author: document.querySelector('meta[name="author"]')?.content || ''
    }
  };

  sendResponse({ success: true, pageInfo });
}

// Handle element highlighting
function handleHighlightElement(request, sendResponse) {
  try {
    const element = document.querySelector(request.selector);
    if (element) {
      DOMUtils.highlightElement(element, request.color);
      DOMUtils.scrollIntoView(element);
      sendResponse({ success: true });
    } else {
      sendResponse({ error: 'Element not found' });
    }
  } catch (error) {
    sendResponse({ error: error.message });
  }
}

// Handle data extraction
function handleExtractData(request, sendResponse) {
  try {
    const elements = document.querySelectorAll(request.selector);
    const data = Array.from(elements).map(element => {
      const item = {};

      if (request.attributes) {
        request.attributes.forEach(attr => {
          if (attr === 'text') {
            item[attr] = element.textContent?.trim();
          } else if (attr === 'html') {
            item[attr] = element.innerHTML;
          } else {
            item[attr] = element.getAttribute(attr);
          }
        });
      } else {
        item.text = element.textContent?.trim();
        item.html = element.innerHTML;
      }

      return item;
    });

    sendResponse({ success: true, data });
  } catch (error) {
    sendResponse({ error: error.message });
  }
}

// Legacy selector generation for backward compatibility
function generateLegacySelector(element) {
  if (element.id) {
    return '#' + element.id;
  }

  if (element.className) {
    const classes = element.className.split(' ').filter(c => c);
    if (classes.length > 0) {
      return '.' + classes.join('.');
    }
  }

  let selector = element.tagName.toLowerCase();
  if (element.parentNode) {
    const siblings = Array.from(element.parentNode.children);
    const index = siblings.indexOf(element);
    if (index > -1) {
      selector += `:nth-child(${index + 1})`;
    }
  }

  return selector;
}

// Recording functionality
let recordingEventListeners = [];

function startInteractionRecording() {
  // Record clicks
  const clickHandler = (event) => {
    if (event.target && !event.target.closest('.webflow-selector-overlay')) {
      const selector = elementSelector ?
        elementSelector.generateOptimalSelector(event.target) :
        generateLegacySelector(event.target);

      recordedActions.push({
        type: 'click',
        selector: selector,
        timestamp: Date.now(),
        element: {
          tagName: event.target.tagName,
          id: event.target.id,
          className: event.target.className
        }
      });
    }
  };

  // Record input changes
  const inputHandler = (event) => {
    if (event.target && (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA')) {
      const selector = elementSelector ?
        elementSelector.generateOptimalSelector(event.target) :
        generateLegacySelector(event.target);

      recordedActions.push({
        type: 'type',
        selector: selector,
        value: event.target.value,
        timestamp: Date.now(),
        element: {
          tagName: event.target.tagName,
          id: event.target.id,
          className: event.target.className
        }
      });
    }
  };

  // Record select changes
  const selectHandler = (event) => {
    if (event.target && event.target.tagName === 'SELECT') {
      const selector = elementSelector ?
        elementSelector.generateOptimalSelector(event.target) :
        generateLegacySelector(event.target);

      recordedActions.push({
        type: 'select',
        selector: selector,
        value: event.target.value,
        text: event.target.options[event.target.selectedIndex]?.text,
        timestamp: Date.now(),
        element: {
          tagName: event.target.tagName,
          id: event.target.id,
          className: event.target.className
        }
      });
    }
  };

  document.addEventListener('click', clickHandler, true);
  document.addEventListener('input', inputHandler, true);
  document.addEventListener('change', selectHandler, true);

  recordingEventListeners = [
    { element: document, event: 'click', handler: clickHandler },
    { element: document, event: 'input', handler: inputHandler },
    { element: document, event: 'change', handler: selectHandler }
  ];
}

function stopInteractionRecording() {
  recordingEventListeners.forEach(({ element, event, handler }) => {
    element.removeEventListener(event, handler, true);
  });
  recordingEventListeners = [];
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (elementSelector) {
    elementSelector.destroy();
  }
  stopInteractionRecording();
});
