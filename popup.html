<!DOCTYPE html>
<html dir="rtl">
<head>
  <meta charset="UTF-8">
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <h2>أداة أتمتة المواقع</h2>
  
  <div class="action-container">
    <div class="action-header">
      <span>الإجراء</span>
      <span>محدد العنصر</span>
      <select id="actionType">
        <option value="text">نص</option>
        <option value="click">نقر</option>
        <option value="wait">انتظار</option>
      </select>
    </div>
    
    <div class="action-params">
      <label>مدة الإنتظار قبل تنفيذ الإجراء</label>
      <input type="text" id="waitTime" placeholder="شرط">
    </div>
    
    <div class="action-buttons">
      <button id="addAction">إضافة</button>
      <button id="executeAction">تنفيذ</button>
    </div>
  </div>
  
  <h3>الإجراءات</h3>
  <div id="actionsList"></div>
  
  <script src="popup.js"></script>
</body>
</html>