<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WebFlow Automator Pro</title>
  <link rel="stylesheet" href="css/popup.css">
  <link rel="stylesheet" href="css/icons.css">
</head>
<body>
  <!-- Header -->
  <header class="header">
    <div class="header-content">
      <div class="logo">
        <img src="icons/icon32.png" alt="WebFlow" class="logo-icon">
        <h1 class="logo-text">WebFlow Automator Pro</h1>
      </div>
      <div class="header-actions">
        <button class="btn-icon" id="settingsBtn" title="الإعدادات">
          <i class="icon-settings"></i>
        </button>
        <button class="btn-icon" id="helpBtn" title="المساعدة">
          <i class="icon-help"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- Navigation Tabs -->
  <nav class="nav-tabs">
    <button class="tab-btn active" data-tab="recorder">
      <i class="icon-record"></i>
      <span>التسجيل</span>
    </button>
    <button class="tab-btn" data-tab="actions">
      <i class="icon-list"></i>
      <span>الإجراءات</span>
    </button>
    <button class="tab-btn" data-tab="projects">
      <i class="icon-folder"></i>
      <span>المشاريع</span>
    </button>
    <button class="tab-btn" data-tab="templates">
      <i class="icon-template"></i>
      <span>القوالب</span>
    </button>
  </nav>

  <!-- Main Content -->
  <main class="main-content">

    <!-- Recorder Tab -->
    <div class="tab-content active" id="recorder-tab">
      <div class="section">
        <div class="section-header">
          <h3>تسجيل الإجراءات</h3>
          <div class="recording-status" id="recordingStatus">
            <span class="status-text">غير نشط</span>
          </div>
        </div>

        <div class="recording-controls">
          <button class="btn btn-primary" id="startRecording">
            <i class="icon-record"></i>
            بدء التسجيل
          </button>
          <button class="btn btn-secondary" id="stopRecording" disabled>
            <i class="icon-stop"></i>
            إيقاف التسجيل
          </button>
          <button class="btn btn-outline" id="pauseRecording" disabled>
            <i class="icon-pause"></i>
            إيقاف مؤقت
          </button>
        </div>

        <div class="recording-info">
          <div class="info-item">
            <span class="label">الإجراءات المسجلة:</span>
            <span class="value" id="recordedCount">0</span>
          </div>
          <div class="info-item">
            <span class="label">مدة التسجيل:</span>
            <span class="value" id="recordingDuration">00:00</span>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-header">
          <h3>إضافة إجراء يدوي</h3>
        </div>

        <div class="action-form">
          <div class="form-group">
            <label for="actionType">نوع الإجراء</label>
            <select id="actionType" class="form-control">
              <option value="click">نقر</option>
              <option value="type">كتابة نص</option>
              <option value="select">اختيار من قائمة</option>
              <option value="hover">تمرير الماوس</option>
              <option value="scroll">تمرير الصفحة</option>
              <option value="wait">انتظار</option>
              <option value="extract">استخراج بيانات</option>
              <option value="condition">شرط</option>
              <option value="loop">حلقة تكرار</option>
              <option value="variable">متغير</option>
              <option value="javascript">كود JavaScript</option>
            </select>
          </div>

          <div class="form-group">
            <label for="elementSelector">محدد العنصر</label>
            <div class="input-group">
              <input type="text" id="elementSelector" class="form-control" placeholder="CSS Selector أو XPath">
              <button class="btn btn-outline" id="selectElement" title="تحديد عنصر من الصفحة">
                <i class="icon-target"></i>
              </button>
            </div>
          </div>

          <div class="form-group" id="actionValueGroup">
            <label for="actionValue">القيمة</label>
            <input type="text" id="actionValue" class="form-control" placeholder="النص أو القيمة">
          </div>

          <div class="form-group">
            <label for="waitBefore">انتظار قبل التنفيذ (مللي ثانية)</label>
            <input type="number" id="waitBefore" class="form-control" value="0" min="0">
          </div>

          <div class="form-group">
            <label for="waitAfter">انتظار بعد التنفيذ (مللي ثانية)</label>
            <input type="number" id="waitAfter" class="form-control" value="0" min="0">
          </div>

          <div class="form-actions">
            <button class="btn btn-primary" id="addAction">
              <i class="icon-plus"></i>
              إضافة الإجراء
            </button>
            <button class="btn btn-outline" id="testAction">
              <i class="icon-play"></i>
              اختبار الإجراء
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions Tab -->
    <div class="tab-content" id="actions-tab">
      <div class="section">
        <div class="section-header">
          <h3>قائمة الإجراءات</h3>
          <div class="section-actions">
            <button class="btn btn-outline" id="clearActions">
              <i class="icon-trash"></i>
              مسح الكل
            </button>
            <button class="btn btn-primary" id="executeActions">
              <i class="icon-play"></i>
              تنفيذ الإجراءات
            </button>
          </div>
        </div>

        <div class="execution-controls">
          <div class="form-group">
            <label for="executionSpeed">سرعة التنفيذ</label>
            <select id="executionSpeed" class="form-control">
              <option value="slow">بطيء (2 ثانية)</option>
              <option value="normal" selected>عادي (1 ثانية)</option>
              <option value="fast">سريع (0.5 ثانية)</option>
              <option value="instant">فوري</option>
            </select>
          </div>

          <div class="form-group">
            <label for="errorHandling">معالجة الأخطاء</label>
            <select id="errorHandling" class="form-control">
              <option value="continue" selected>متابعة عند الخطأ</option>
              <option value="stop">إيقاف عند الخطأ</option>
            </select>
          </div>

          <div class="checkbox-group">
            <label class="checkbox">
              <input type="checkbox" id="highlightElements" checked>
              <span class="checkmark"></span>
              إبراز العناصر أثناء التنفيذ
            </label>
          </div>
        </div>

        <div class="actions-list" id="actionsList">
          <div class="empty-state">
            <i class="icon-list"></i>
            <h4>لا توجد إجراءات</h4>
            <p>ابدأ بتسجيل الإجراءات أو إضافة إجراء يدوي</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Projects Tab -->
    <div class="tab-content" id="projects-tab">
      <div class="section">
        <div class="section-header">
          <h3>إدارة المشاريع</h3>
          <div class="section-actions">
            <button class="btn btn-outline" id="importProject">
              <i class="icon-upload"></i>
              استيراد
            </button>
            <button class="btn btn-primary" id="newProject">
              <i class="icon-plus"></i>
              مشروع جديد
            </button>
          </div>
        </div>

        <div class="projects-list" id="projectsList">
          <div class="empty-state">
            <i class="icon-folder"></i>
            <h4>لا توجد مشاريع</h4>
            <p>أنشئ مشروعك الأول لحفظ وتنظيم الإجراءات</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Templates Tab -->
    <div class="tab-content" id="templates-tab">
      <div class="section">
        <div class="section-header">
          <h3>القوالب الجاهزة</h3>
          <div class="section-actions">
            <button class="btn btn-outline" id="refreshTemplates">
              <i class="icon-refresh"></i>
              تحديث
            </button>
          </div>
        </div>

        <div class="templates-grid" id="templatesGrid">
          <div class="empty-state">
            <i class="icon-template"></i>
            <h4>لا توجد قوالب</h4>
            <p>سيتم إضافة قوالب جاهزة للمواقع الشائعة قريباً</p>
          </div>
        </div>
      </div>
    </div>

  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-content">
      <div class="status-info">
        <span class="status-dot" id="connectionStatus"></span>
        <span class="status-text" id="statusText">جاهز</span>
      </div>
      <div class="version-info">
        <span>الإصدار 2.0.0</span>
      </div>
    </div>
  </footer>

  <!-- Modals and Overlays -->
  <div class="modal-overlay" id="modalOverlay"></div>

  <!-- Settings Modal -->
  <div class="modal" id="settingsModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>الإعدادات</h3>
        <button class="btn-close" id="closeSettings">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <!-- Settings content will be loaded here -->
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="js/utils/helpers.js"></script>
  <script src="js/popup/ui-manager.js"></script>
  <script src="js/popup/project-manager.js"></script>
  <script src="js/popup/template-manager.js"></script>
  <script src="js/popup.js"></script>
</body>
</html>