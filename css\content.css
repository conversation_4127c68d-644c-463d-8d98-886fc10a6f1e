/**
 * WebFlow Automator Pro - Content Styles
 * Styles for content script overlays and UI elements
 */

/* Element Selector Overlay */
.webflow-selector-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(0, 0, 0, 0.3) !important;
  z-index: 999999 !important;
  cursor: crosshair !important;
  display: none !important;
  backdrop-filter: blur(2px) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Element Highlight Box */
.webflow-highlight-box {
  position: absolute !important;
  border: 3px solid #ff6b6b !important;
  background: rgba(255, 107, 107, 0.1) !important;
  pointer-events: none !important;
  z-index: 1000000 !important;
  display: none !important;
  border-radius: 4px !important;
  box-shadow: 0 0 10px rgba(255, 107, 107, 0.5) !important;
  transition: all 0.1s ease !important;
}

/* Selector Tooltip */
.webflow-selector-tooltip {
  position: absolute !important;
  top: -35px !important;
  left: 0 !important;
  background: #333 !important;
  color: white !important;
  padding: 5px 10px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  white-space: nowrap !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  max-width: 300px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Instructions Overlay */
.webflow-instructions {
  position: fixed !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: #333 !important;
  color: white !important;
  padding: 15px 25px !important;
  border-radius: 8px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  z-index: 1000001 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  text-align: center !important;
  direction: rtl !important;
  animation: webflow-fade-in 0.3s ease !important;
}

/* Recording Indicator */
.webflow-recording-indicator {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: #ff4757 !important;
  color: white !important;
  padding: 10px 15px !important;
  border-radius: 20px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  z-index: 1000001 !important;
  box-shadow: 0 2px 10px rgba(255, 71, 87, 0.3) !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  animation: webflow-pulse 2s infinite !important;
}

.webflow-recording-indicator::before {
  content: '' !important;
  width: 8px !important;
  height: 8px !important;
  background: white !important;
  border-radius: 50% !important;
  animation: webflow-blink 1s infinite !important;
}

/* Execution Progress */
.webflow-execution-progress {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  padding: 15px 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 1000001 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  min-width: 300px !important;
  max-width: 500px !important;
  animation: webflow-slide-up 0.3s ease !important;
}

.webflow-execution-progress .title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #333 !important;
  margin-bottom: 10px !important;
  text-align: center !important;
}

.webflow-execution-progress .progress-bar {
  width: 100% !important;
  height: 6px !important;
  background: #f0f0f0 !important;
  border-radius: 3px !important;
  overflow: hidden !important;
  margin-bottom: 10px !important;
}

.webflow-execution-progress .progress-fill {
  height: 100% !important;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 3px !important;
  transition: width 0.3s ease !important;
}

.webflow-execution-progress .current-action {
  font-size: 12px !important;
  color: #666 !important;
  text-align: center !important;
  direction: rtl !important;
}

/* Element Highlight Variants */
.webflow-highlight-success {
  border-color: #2ed573 !important;
  background: rgba(46, 213, 115, 0.1) !important;
  box-shadow: 0 0 10px rgba(46, 213, 115, 0.5) !important;
}

.webflow-highlight-error {
  border-color: #ff4757 !important;
  background: rgba(255, 71, 87, 0.1) !important;
  box-shadow: 0 0 10px rgba(255, 71, 87, 0.5) !important;
}

.webflow-highlight-warning {
  border-color: #ffa502 !important;
  background: rgba(255, 165, 2, 0.1) !important;
  box-shadow: 0 0 10px rgba(255, 165, 2, 0.5) !important;
}

.webflow-highlight-info {
  border-color: #3742fa !important;
  background: rgba(55, 66, 250, 0.1) !important;
  box-shadow: 0 0 10px rgba(55, 66, 250, 0.5) !important;
}

/* Notification Toast */
.webflow-toast {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  padding: 15px 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 1000002 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  max-width: 350px !important;
  animation: webflow-slide-in-right 0.3s ease !important;
}

.webflow-toast.success {
  border-left: 4px solid #2ed573 !important;
}

.webflow-toast.error {
  border-left: 4px solid #ff4757 !important;
}

.webflow-toast.warning {
  border-left: 4px solid #ffa502 !important;
}

.webflow-toast.info {
  border-left: 4px solid #3742fa !important;
}

.webflow-toast .title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #333 !important;
  margin-bottom: 5px !important;
}

.webflow-toast .message {
  font-size: 12px !important;
  color: #666 !important;
  line-height: 1.4 !important;
}

/* Context Menu */
.webflow-context-menu {
  position: fixed !important;
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 1000003 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  min-width: 180px !important;
  padding: 5px 0 !important;
  animation: webflow-fade-in 0.2s ease !important;
}

.webflow-context-menu .item {
  padding: 8px 15px !important;
  font-size: 13px !important;
  color: #333 !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

.webflow-context-menu .item:hover {
  background: #f5f5f5 !important;
}

.webflow-context-menu .item.disabled {
  color: #ccc !important;
  cursor: not-allowed !important;
}

.webflow-context-menu .separator {
  height: 1px !important;
  background: #e0e0e0 !important;
  margin: 5px 0 !important;
}

/* Animations */
@keyframes webflow-fade-in {
  from {
    opacity: 0 !important;
    transform: translateX(-50%) translateY(-10px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateX(-50%) translateY(0) !important;
  }
}

@keyframes webflow-slide-up {
  from {
    opacity: 0 !important;
    transform: translateX(-50%) translateY(20px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateX(-50%) translateY(0) !important;
  }
}

@keyframes webflow-slide-in-right {
  from {
    opacity: 0 !important;
    transform: translateX(20px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateX(0) !important;
  }
}

@keyframes webflow-pulse {
  0%, 100% {
    transform: scale(1) !important;
  }
  50% {
    transform: scale(1.05) !important;
  }
}

@keyframes webflow-blink {
  0%, 50% {
    opacity: 1 !important;
  }
  51%, 100% {
    opacity: 0.3 !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .webflow-instructions {
    background: #2c2c2c !important;
    color: #f0f0f0 !important;
  }
  
  .webflow-execution-progress {
    background: #2c2c2c !important;
    border-color: #404040 !important;
    color: #f0f0f0 !important;
  }
  
  .webflow-execution-progress .title {
    color: #f0f0f0 !important;
  }
  
  .webflow-execution-progress .current-action {
    color: #ccc !important;
  }
  
  .webflow-toast {
    background: #2c2c2c !important;
    border-color: #404040 !important;
  }
  
  .webflow-toast .title {
    color: #f0f0f0 !important;
  }
  
  .webflow-toast .message {
    color: #ccc !important;
  }
  
  .webflow-context-menu {
    background: #2c2c2c !important;
    border-color: #404040 !important;
  }
  
  .webflow-context-menu .item {
    color: #f0f0f0 !important;
  }
  
  .webflow-context-menu .item:hover {
    background: #404040 !important;
  }
  
  .webflow-context-menu .separator {
    background: #404040 !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .webflow-highlight-box {
    border-width: 4px !important;
    box-shadow: 0 0 0 2px white, 0 0 0 4px #ff6b6b !important;
  }
  
  .webflow-selector-tooltip {
    border: 2px solid white !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .webflow-highlight-box,
  .webflow-instructions,
  .webflow-execution-progress,
  .webflow-toast,
  .webflow-context-menu {
    animation: none !important;
    transition: none !important;
  }
  
  .webflow-recording-indicator {
    animation: none !important;
  }
  
  .webflow-recording-indicator::before {
    animation: none !important;
  }
}
